package zx.vanx.shop_item;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * author
 * Date: 2023/7/517:10
 **/
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {
        "zx.vanx.ma_manage.client","zx.vanx.as_media.client","zx.vanx.pay_withdra.client"})
@EnableScheduling
@MapperScan("zx.vanx.shop_item.mapper")
@ComponentScan(basePackages = "zx.vanx")
public class ShopItemApplication {
    public static void main(String[] args) {
        SpringApplication.run(ShopItemApplication.class);
    }
}
