package zx.vanx.shop_item.controller.frontend;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.utils.R;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.service.MinioService;
import zx.vanx.shop_item.excel.SexCategAttrValueExcel;
import zx.vanx.shop_item.service.ExcelService;
import zx.vanx.util.EasyExcelUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Api(tags = "a.Excel模块")
@RestController
@RequiredArgsConstructor
@RequestMapping("/shop_item/excel")
public class ExcelController  {

    private final ExcelService excelService;

    private final MinioService minioService;

    @ApiOperation(value = "测试RequiresUserId注解")
    
    @PostMapping("test")
    public ResultData<String> testUploadMinio(HttpServletRequest http) {

        // 方法2,通过工具类获取用户id
        Long utilsGetUserId = LoginHelper.getUserId();

        // 方法3,通过注解,继承类切面获取用户id
        Long annotationGetUserId = LoginHelper.getUserId();

        return ResultData.ok(LoginHelper.getUserId() + "");
    }

    @ApiOperation(value = "导入景区基本信息以及景区属性等excel数据")
    @PostMapping("import-scenic-info")
    public ResultData<List<SexCategAttrValueExcel>> importScenicInfo(MultipartFile file) {

        List<SexCategAttrValueExcel> listByFile = EasyExcelUtils.getListByFile(file, SexCategAttrValueExcel.class);
        List<SexCategAttrValueExcel> sexCategAttrValueExcels = excelService.importScenicInfo(listByFile);
        if (!CollectionUtils.isEmpty(sexCategAttrValueExcels)) {
            return ResultData.ok(sexCategAttrValueExcels).Message("导入景区已存在");
        }
        return ResultData.ok();
    }


    @ApiOperation(value = "excel景区信息信息模板下载")
    @GetMapping("scenic-info-excel-model")
    public R buildingInfoExcelModel(HttpServletResponse response) {
        List<SexCategAttrValueExcel> list = new ArrayList<>();
        SexCategAttrValueExcel sexCategAttrValueExcel = new SexCategAttrValueExcel();
        list.add(sexCategAttrValueExcel);
        try {
            EasyExcelUtils.resposeEasyExcel(response,"景区信息",list);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return R.ok().message("下载成功");
    }
}
