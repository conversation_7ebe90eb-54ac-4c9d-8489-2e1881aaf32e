package zx.vanx.shop_item.controller.frontend;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import zx.vanx.as_media.client.SysMediaClient;
import zx.vanx.as_media.dto.UserMediaPermissionDTO;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.core.page.PageObj;
import zx.vanx.common_util.exception.ZxException;
import zx.vanx.common_util.utils.HTTPJsonUtil;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.service.MinioService;
import zx.vanx.shop_item.dto.GiftDto;
import zx.vanx.shop_item.entity.PlatfItemCategory;
import zx.vanx.shop_item.query.GiftPageQuery;
import zx.vanx.shop_item.request.GiftIncomeRequest;
import zx.vanx.shop_item.request.GiftSendQueryRequest;
import zx.vanx.shop_item.request.SendGiftRequest;
import zx.vanx.shop_item.request.SingleWorksGiftIncomeRequest;
import zx.vanx.shop_item.service.ItemInfoService;
import zx.vanx.shop_item.service.PlatfItemCategoryService;
import zx.vanx.shop_item.service.UserGiftTransacRecService;
import zx.vanx.shop_item.vo.GiftIncomeVo;
import zx.vanx.shop_item.vo.GiftVo;
import zx.vanx.user_permiss.entity.UserInfoFast;
import zx.vanx.user_permiss.redis.service.UserInfoFastRedisService;
import zx.vanx.vo.ImageData;
import zx.vanx.vo.IncomeAndExpenditureDetailVo;

import java.math.BigDecimal;
import java.util.List;

@Api(tags = "b.礼物管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/shop_item/gift")
public class GiftController  {

    private final ItemInfoService itemInfoService;

    private final PlatfItemCategoryService platfItemCategoryService;

    private final MinioService minioService;

    private final UserInfoFastRedisService userInfoFastRedisService;

    private final SysMediaClient sysMediaClient;

    private final UserGiftTransacRecService userGiftTransacRecService;

    /**
     * 添加-礼物
     * @param obj 礼物信息
     * @param file1 图片文件
     * @param file2 json文件
     * @param file3 git文件
     * @return 是否成功
     */
    @ApiOperation(value = "添加-礼物")
    @PostMapping("save-gift")
    public ResultData<Void> saveGift(@RequestParam(value = "obj") String obj,
                                     @RequestPart(value = "file1") MultipartFile file1,
                                     @RequestPart(value = "file2") MultipartFile file2,
                                     @RequestPart(value = "file3") MultipartFile file3) {
        Long currentUserId = LoginHelper.getUserId();

        // 获取前端数据
        GiftDto giftDto = HTTPJsonUtil.parseObj(obj, GiftDto.class);

        // 上传文件
        ImageData file1ImageData = minioService.uploadFile(file1, currentUserId);
        giftDto.setPicName(file1ImageData.getFileName());
        giftDto.setPicUrl(file1ImageData.getUrl());

        // 上传文件
        ImageData file2ImageData = minioService.uploadFile(file2, currentUserId);
        giftDto.setPlayJsonFileName(file2ImageData.getFileName());
        giftDto.setPlayJsonFileUrl(file2ImageData.getUrl());

        // 上传文件
        ImageData file3ImageData = minioService.uploadFile(file3, currentUserId);
        giftDto.setPlayGitFileName(file3ImageData.getFileName());
        giftDto.setPlayGitFileUrl(file3ImageData.getUrl());

        // 获取登录用户id
        giftDto.setCreatorId(LoginHelper.getUserId());

        // 添加礼物
        itemInfoService.saveGift(giftDto);
        return ResultData.ok();
    }

    /**
     * 删除-礼物
     * @param itemId 礼物id
     * @return 是否成功
     */
    @ApiOperation(value = "删除-礼物")
    @PostMapping("remove-gift/{itemId}")
    public ResultData<Object> removeGift(@PathVariable(value = "itemId") Long itemId) {

        itemInfoService.removeGift(itemId);
        return ResultData.ok().Message("删除成功");

    }

    /**
     * 分页-查询-礼物-列表
     * @param giftPageQuery 查询对象
     * @return 礼物列表
     */
    @ApiOperation(value = "分页-查询-礼物-列表")
    @PostMapping("query-gift-page-list")
    public ResultData<PageObj<GiftVo>> queryGiftPageList(@RequestBody GiftPageQuery giftPageQuery) {

        PageObj<GiftVo> giftVoPageObj = itemInfoService.queryGiftPageList(giftPageQuery);

        return ResultData.ok(giftVoPageObj);
    }

    /**
     * 查询-App礼物-列表
     * @param itemCategName 分类名称
     * @return App礼物列表
     */
    @ApiOperation(value = "查询-App礼物-列表")
    @PostMapping("query-app-gift-list")
    public ResultData<List<GiftVo>> queryAppGiftList(@RequestParam(value = "itemCategName", required = false) String itemCategName) {

        List<GiftVo> giftVoPageObj = itemInfoService.queryAppGiftList(itemCategName);
        return ResultData.ok(giftVoPageObj);
    }

    /**
     * 修改商品积分兑换状态
     * @param itemCategName 商品id
     * @return 是否成功
     */
    @ApiOperation("查询-礼物目录列表")
    @GetMapping("/gift-category/{itemCategName}")
    public ResultData<PlatfItemCategory> queryGiftCategory(@PathVariable(value = "itemCategName") String itemCategName) {
        PlatfItemCategory platfItemCategory = platfItemCategoryService.queryGiftCategory(itemCategName);
        return ResultData.ok(platfItemCategory);
    }

    /**
     * 查询-礼物类别列表
     * @param itemCategName 分类名称
     * @return 分类列表
     */
    @ApiOperation("查询-礼物类别列表")
    @GetMapping("/gift-category-list/{itemCategName}")
    public ResultData<List<PlatfItemCategory>> queryGiftCategoryList(@PathVariable(value = "itemCategName") String itemCategName) {
        List<PlatfItemCategory> platfItemCategoryList = platfItemCategoryService.queryItemCategoryList(itemCategName);
        return ResultData.ok(platfItemCategoryList);
    }

    /**
     * 查询-礼物类别
     * @param platfItemCategory 分类信息
     * @return 分类列表
     */
    @ApiOperation("添加-礼物分类")
    @PostMapping("/save")
    public ResultData<Void> save(@RequestBody PlatfItemCategory platfItemCategory) {

        platfItemCategory.setCreatorId(LoginHelper.getUserId());
        platfItemCategory.setIsUsed(true);
        platfItemCategory.setCategoryStatus("通过");
        platfItemCategory.setAddSource("平台添加");
        platfItemCategory.setItemCategLevel(2);
        PlatfItemCategory giftCategoryOne = platfItemCategoryService.queryGiftCategoryOne("礼物");
        platfItemCategory.setParentCategId(giftCategoryOne.getItemCategId());

        List<PlatfItemCategory> platfItemCategoryList = platfItemCategoryService.getBaseMapper().selectList(new QueryWrapper<PlatfItemCategory>().eq("item_categ_name", platfItemCategory.getItemCategName()));
        if (!CollectionUtils.isEmpty(platfItemCategoryList)) {
            throw new ZxException(201,"分类名称重复");
        }

        platfItemCategoryService.save(platfItemCategory);
        return ResultData.ok();
    }

    /**
     * 修改启用状态
     * @param platfItemCategory 分类信息
     * @return 分类列表
     */
    @ApiOperation("修改礼物分类启用状态")
    @GetMapping("/update-giftCategory-status")
    public ResultData<String> updateGiftCategoryStatus(@RequestBody PlatfItemCategory platfItemCategory) {
        platfItemCategoryService.updateGiftCategoryStatus(platfItemCategory);
        return ResultData.ok("修改成功");
    }

    /**
     * 查询-礼物类别列表
     * @param itemCategId 父级分类id
     * @return 分类列表
     */
    @ApiOperation("删除-礼物分类")
    @PostMapping("/platf-remove/{itemCategId}")
    public ResultData<Object> platfRemove(@PathVariable(value = "itemCategId") Long itemCategId) {
        platfItemCategoryService.platfRemove(itemCategId);
        return ResultData.ok().Message("删除成功");
    }

    /**
     * 查询-分类对象带children
     * @param platfItemCategory 分类信息
     * @return 分类对象
     */
    @ApiOperation("修改-商品分类")
    @PostMapping("/update")
    public ResultData<Object> update(@RequestBody PlatfItemCategory platfItemCategory) {
        platfItemCategoryService.updateById(platfItemCategory);
        return ResultData.ok();
    }

    @ApiOperation("用户赠送礼物")
    @PostMapping("/send-gift")
    
    public ResultData<Object> sendGift(@RequestBody SendGiftRequest sendGiftRequest) {

        sendGiftRequest.setSenderId(LoginHelper.getUserId());

        if (sendGiftRequest.getSenderId().equals(sendGiftRequest.getReceiverId())) {
            throw new ZxException(203,"不能给自己送礼物");
        }

        // 查询视频是否设置允许送礼物
        UserMediaPermissionDTO userMediaPermissionDTO = sysMediaClient.selectUserMediaPermission(sendGiftRequest.getGiftObjectId(),sendGiftRequest.getReceiverId());
        if (!userMediaPermissionDTO.isAllowSendGifts()) {
            throw new ZxException(201,"该商品不允许送礼物");
        }

        // 判断用户是否有虚拟币且足够买礼物
        if (!getUserIsEnoughVirtualCurrency(LoginHelper.getUserId(),sendGiftRequest)) {
            throw new ZxException(202,"彩钻不足请充值");
        }

        // 送礼物
        platfItemCategoryService.sendGift(LoginHelper.getUserId(),sendGiftRequest);
        return ResultData.ok();

    }

    /**
     * 查询-礼物收益列表
     * @param giftIncomeRequest 分类信息
     * @return 分类对象
     */
    @ApiOperation("查询-礼物收入列表")
    @PostMapping("/query-gift-income-list")
    
    public ResultData<List<GiftIncomeVo>> queryGiftIncomeList(@RequestBody GiftIncomeRequest giftIncomeRequest) {
        List<GiftIncomeVo> giftIncomeVoList = userGiftTransacRecService.queryGiftIncomeList(LoginHelper.getUserId(),giftIncomeRequest);
        return ResultData.ok(giftIncomeVoList);
    }

    /**
     * 查询-礼物收益列表
     * @param singleWorksGiftIncomeRequest 分类信息
     * @return 分类对象
     */
    @ApiOperation("查询-单个作品-礼物收入列表")
    @PostMapping("/query-single-works-gift-income-list")
    
    public ResultData<List<GiftIncomeVo>> querySingleWorksGiftIncomeList(@RequestBody SingleWorksGiftIncomeRequest singleWorksGiftIncomeRequest) {
        List<GiftIncomeVo> giftIncomeVoList = userGiftTransacRecService.querySingleWorksGiftIncomeList(LoginHelper.getUserId(),singleWorksGiftIncomeRequest);
        return ResultData.ok(giftIncomeVoList);
    }

    /**
     * 查询-礼物收益列表
     * @param giftSendQueryRequest 分类信息
     * @return 分类对象
     */
    @ApiOperation("查询-礼物赠送列表")
    @PostMapping("/query-gift-sender-list")
    
    public ResultData<List<IncomeAndExpenditureDetailVo<GiftIncomeVo>>> queryGiftSenderList(@RequestBody GiftSendQueryRequest giftSendQueryRequest) {

        giftSendQueryRequest.setUserId(LoginHelper.getUserId());
        List<IncomeAndExpenditureDetailVo<GiftIncomeVo>> giftIncomeVoList = userGiftTransacRecService.queryGiftSenderList(giftSendQueryRequest);
        return ResultData.ok(giftIncomeVoList);
    }

    /**
     * 判断用户是否有虚拟币且足够买礼物
     * @param userId 用户id
     * @param sendGiftRequest app浏览视频送礼物请求参数
     * @return 用户是否有虚拟币且足够买礼物
     */
    private boolean getUserIsEnoughVirtualCurrency(Long userId, SendGiftRequest sendGiftRequest) {

        // 虚拟币总价值
        BigDecimal virtuCurrencyTotalValue = sendGiftRequest.getVirtuCurrencyTotalValue();

        UserInfoFast userInfoFast = userInfoFastRedisService.getUserInfoFastByUserId(userId);

        BigDecimal userVirtualBalance = userInfoFast.getUserVirtualBalance();

        int result = userVirtualBalance.compareTo(virtuCurrencyTotalValue);

        return result >= 0;
    }

}
