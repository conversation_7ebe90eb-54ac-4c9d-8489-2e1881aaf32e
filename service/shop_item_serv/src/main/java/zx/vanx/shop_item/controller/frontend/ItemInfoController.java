package zx.vanx.shop_item.controller.frontend;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.core.page.PageObj;
import zx.vanx.common_util.exception.ZxException;
import zx.vanx.common_util.utils.ObjectUtil;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.ma_manage.dto.AddVanxPointsItemRelatDTO;
import zx.vanx.shop_item.query.ItemInfoPageQuery;
import zx.vanx.shop_item.request.SpuInfoAddRequest;
import zx.vanx.shop_item.service.ItemInfoService;
import zx.vanx.shop_item.vo.ActivityItemVo;
import zx.vanx.shop_item.vo.ItemInfoVo;
import zx.vanx.shop_item.vo.MyVirtualBalanceVo;

import java.math.BigDecimal;
import java.util.List;


@Api(tags = "c.商品管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/shop_item/item-info")
public class ItemInfoController  {

    private final ItemInfoService itemInfoService;

//    /**
//     * 添加-商品
//     * @param obj 商品信息
//     * @param itemSkuImageFiles sku图片文件
//     * @return 是否成功
//     */
//    @ApiOperation(value = "添加-商品")
//    @PostMapping("save-item")
//    
//    public ResultData<Void> saveItem(@RequestParam(value = "obj") String obj,@RequestParam(value = "list") String list
//            ,@RequestPart(value = "files") MultipartFile[] itemSkuImageFiles) {
//
//        // 获取前端数据
//        ItemInfoDto itemInfoDto = HTTPJsonUtil.parseObj(obj, ItemInfoDto.class);
//        List<ItemCategAttrVo> itemCategAttrVoList = HTTPJsonUtil.parseList(list, ItemCategAttrVo.class);
//        // 设置创建者id
//        itemInfoDto.setCreatorId(userId);
//        // 设置销售者id
//        itemInfoDto.setShellId(userId);
//
//        // 校验积分兑换状态
//        if (ObjectUtil.isEmpty(itemInfoDto.getIsPointsExchange())) {
//            throw new ZxException(204,"是否积分兑换不能为空");
//        } else {
//            if (ObjectUtil.isEmpty(itemInfoDto.getNeededPointsValue())) {
//                throw new ZxException(204,"积分值不能为空");
//            }
//        }
//
//        // 校验店铺id
//        if (ObjectUtil.isEmpty(itemInfoDto.getShopId())) {
//            throw new ZxException(204,"店铺id不能为空");
//        }
//
//        // 校验商品名称
//        if (ObjectUtil.isEmpty(itemInfoDto.getItemName())) {
//            throw new ZxException(204,"商品名称不能为空");
//        }
//
//        // 校验商品一级分类id
//        if (ObjectUtil.isEmpty(itemInfoDto.getPlatformFirstCid())) {
//            throw new ZxException(204,"商品一级分类id不能为空");
//        }
//
//        // 校验商品二级分类id
//        if (ObjectUtil.isEmpty(itemInfoDto.getPlatformSecondCid())) {
//            throw new ZxException(204,"商品二级分类id不能为空");
//        }
//
//        // 校验商品货币类型
//        if (ObjectUtil.isEmpty(itemInfoDto.getCurrencyType())) {
//            throw new ZxException(204,"商品货币类型不能为空");
//        }
//
//        // 校验商品价格
//        if (ObjectUtil.isEmpty(itemInfoDto.getSellPrice())) {
//            throw new ZxException(204,"商品价格不能为空");
//        }
//
//        // 添加商品
//        itemInfoService.saveItem(itemInfoDto,itemCategAttrVoList,itemSkuImageFiles);
//        return ResultData.ok();
//
//    }

    @ApiOperation("查询商品详情")
    
    @PostMapping("/activity-item-detail")
    public ActivityItemVo queryActivityItemDetail(@RequestParam Long itemId) {

        return itemInfoService.queryActivityItemDetail(itemId, LoginHelper.getUserId());

    }

    /**
     * 添加-商品基本信息
     * @param spuInfoAddRequest 商品信息
     * @return 是否成功
     */
    @ApiOperation(value = "添加-商品信息")
    @PostMapping("save-item-info")
    
    public ResultData<Void> saveItemInfo(@RequestBody SpuInfoAddRequest spuInfoAddRequest) {

        // 设置创建者id
        spuInfoAddRequest.setCreatorId(LoginHelper.getUserId());
        // 设置销售者id
        spuInfoAddRequest.setSellerId(LoginHelper.getUserId());

        // 校验店铺id
        if (ObjectUtil.isEmpty(spuInfoAddRequest.getShopId())) {
            throw new ZxException(204,"店铺id不能为空");
        }

        // 校验商品名称
        if (ObjectUtil.isEmpty(spuInfoAddRequest.getItemName())) {
            throw new ZxException(204,"商品名称不能为空");
        }

        // 校验商品一级分类id
        if (ObjectUtil.isEmpty(spuInfoAddRequest.getPlatformFirstCid())) {
            throw new ZxException(204,"商品一级分类id不能为空");
        }

        // 校验商品二级分类id
        if (ObjectUtil.isEmpty(spuInfoAddRequest.getPlatformSecondCid())) {
            throw new ZxException(204,"商品二级分类id不能为空");
        }

        // 校验商品价格
        if (CollectionUtils.isEmpty(spuInfoAddRequest.getSkuList())) {
            throw new ZxException(204,"商品sku不能为空");
        }

        // 添加商品
        itemInfoService.saveItemInfo(spuInfoAddRequest);
        return ResultData.ok();
    }

    /**
     * 分页-查询-商品-列表
     * @param giftPageQuery 查询对象
     * @return 商品列表
     */
    @ApiOperation(value = "分页-查询-商品-列表")
    @PostMapping("query-item-page-list")
    public ResultData<PageObj<ItemInfoVo>> queryItemPageList(@RequestBody ItemInfoPageQuery giftPageQuery) {
        PageObj<ItemInfoVo> itemInfoVoPageObj = itemInfoService.queryItemPageList(giftPageQuery);
        return ResultData.ok(itemInfoVoPageObj);
    }

    /**
     * 根据分类id查询商品列表
     * @param categoryId 查询对象
     * @return 商品列表
     */
    @ApiOperation(value = "根据分类id查询商品列表")
    @PostMapping("query-item-list-by-category")
    public ResultData<List<ActivityItemVo>> queryItemListByCategory(@RequestParam Long categoryId) {
        List<ActivityItemVo> activityItemVoList = itemInfoService.queryItemListByCategory(categoryId);
        return ResultData.ok(activityItemVoList);
    }

    /**
     * 查询我的虚拟币页面信息
     * @return 我的虚拟币页面信息
     */
    @ApiOperation(value = "查询我的虚拟币页面信息")
    @PostMapping("query-my-virtual-balance")
    public ResultData<MyVirtualBalanceVo> queryMyVirtualBalance() {

        MyVirtualBalanceVo myVirtualBalanceVo = itemInfoService.queryMyVirtualBalance();
        return ResultData.ok(myVirtualBalanceVo);
    }

    /**
     * 修改商品积分兑换状态
     * @param itemId 商品id
     * @return 是否成功
     */
    @ApiOperation(value = "修改商品积分兑换状态")
    @PostMapping("update-item-points-exchange-statu")
    public Boolean updateItemPointsExchangeStatu(@RequestParam(value = "itemId") Long itemId) {

        return itemInfoService.updateItemPointsExchangeStatu(itemId);
    }

    /**
     * 积分兑换V币
     * @param addVanxPointsItemRelatDTO 积分兑换活动商品关联表
     * @param money 兑换虚拟币金额
     * @return 结果
     */
    @ApiOperation(value = "积分兑换V币")
    @PostMapping("points-exchange-item/{money}")
    public ResultData<Object> pointsExchangeItem(@RequestBody AddVanxPointsItemRelatDTO addVanxPointsItemRelatDTO
            ,@PathVariable(value = "money") BigDecimal money){

        Boolean flag = itemInfoService.pointsExchangeItem(addVanxPointsItemRelatDTO, money);

        if (flag) {
            return ResultData.ok().Message("兑换成功");
        } else {
            return ResultData.error("兑换失败");
        }
    }

    /**
     * 修改商品积分兑换状态佳欢
     * @param itemId 商品id
     * @return 是否成功
     */
    @ApiOperation(value = "修改商品积分兑换状态佳欢")
    @PostMapping("update-item-points-exchange-status")
    public Boolean updateItemPointsExchangeStatus(@RequestParam(value = "itemId") Long itemId) {
        return itemInfoService.updateItemPointsExchangeStatus(itemId);
    }

}
