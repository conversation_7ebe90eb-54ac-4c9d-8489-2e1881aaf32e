package zx.vanx.shop_item.controller.frontend;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.core.page.PageObj;
import zx.vanx.common_util.exception.ZxException;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.shop_item.entity.ItemCategAttr;
import zx.vanx.shop_item.entity.PlatfItemCategory;
import zx.vanx.shop_item.request.AddItemCategAttrRequest;
import zx.vanx.shop_item.request.AddItemCategAttrValueRequest;
import zx.vanx.shop_item.service.PlatfItemCategoryService;
import zx.vanx.shop_item.vo.ItemCategAttrValueVo;
import zx.vanx.shop_item.vo.ItemCategAttrVo;
import zx.vanx.shop_item.vo.itemCategVo;

import java.util.List;


@Api(tags = "d.商品分类")
@RestController
@RequiredArgsConstructor
@RequestMapping("/shop_item/platf-item-category")
public class PlatfItemCategoryController {

    private final PlatfItemCategoryService platfItemCategoryService;

    /**
     * 添加-商品分类
     * @param platfItemCategory 商品分类
     * @return 是否成功
     */
    @ApiOperation("添加-商品分类")
    @PostMapping("/save")
    public ResultData<Void> save(@RequestBody PlatfItemCategory platfItemCategory) {

        platfItemCategory.setCreatorId(LoginHelper.getUserId());
        platfItemCategory.setIsUsed(true);
        platfItemCategory.setCategoryStatus("通过");
        platfItemCategory.setAddSource("平台添加");

        List<PlatfItemCategory> platfItemCategoryList = platfItemCategoryService.getBaseMapper().selectList(new QueryWrapper<PlatfItemCategory>().eq("item_categ_name", platfItemCategory.getItemCategName()));
        if (!CollectionUtils.isEmpty(platfItemCategoryList)) {
            throw new ZxException(201,"分类名称重复");
        }
        platfItemCategoryService.save(platfItemCategory);
        return ResultData.ok();
    }

    /**
     * 查询-平台分类-tree
     * @return 分类列表
     */
    @ApiOperation("查询-商品分类-tree（无礼物）")
    @PostMapping("/tree")
    public ResultData<List<PlatfItemCategory>> tree() {
        List<PlatfItemCategory> platfItemCategoryList = platfItemCategoryService.tree();
        return ResultData.ok(platfItemCategoryList);
    }

    @ApiOperation("查询-商品分类-tree（所有）")
    @PostMapping("/tree-all")
    public ResultData<List<PlatfItemCategory>> treeAll() {
        List<PlatfItemCategory> platfItemCategoryList = platfItemCategoryService.treeAll();
        return ResultData.ok(platfItemCategoryList);
    }

    /**
     * 查询-商品类别列表
     * @param itemCategName 分类名称
     * @return 分类列表
     */
    @ApiOperation("查询-二级商品类别列表")
    @GetMapping("/item-category-list/{itemCategName}")
    public ResultData<List<PlatfItemCategory>> queryItemCategoryList(@ApiParam(value = "一级分类名称") @PathVariable(value = "itemCategName") String itemCategName) {

        List<PlatfItemCategory> platfItemCategoryList = platfItemCategoryService.queryItemCategoryList(itemCategName);
        return ResultData.ok(platfItemCategoryList);
    }

    /**
     * 查询-商品类别
     * @param itemCategId 分类id
     * @param page 页码
     * @param limit 每页数量
     * @return 分类列表
     */
    @ApiOperation("查询-平台分类-列表")
    @PostMapping("/list/{itemCategId}")
    public ResultData<PageObj<PlatfItemCategory>> list(@PathVariable(value = "itemCategId") Long itemCategId,
                                                       @RequestParam(value = "page",required = false) Long page,
                                                       @RequestParam(value = "limit",required = false) Long limit) {
        PageObj<PlatfItemCategory> platfItemCategoryPageObj = platfItemCategoryService.list(itemCategId,page,limit);
        return ResultData.info("查询成功",platfItemCategoryPageObj);
    }

    @ApiOperation("查询-指定商品分类-tree")
    @PostMapping("/specify-item-category-tree")
    public ResultData<List<PlatfItemCategory>> specifyItemCategoryTree(
                                                       @RequestParam(value = "itemCategName",required = false) String itemCategName) {
        List<PlatfItemCategory> platfItemCategoryList = platfItemCategoryService.specifyItemCategoryTree(itemCategName);
        return ResultData.info("查询成功",platfItemCategoryList);

    }

    /**
     * 查询-礼物类别
     * @param itemCategId 分类id
     * @return 分类列表
     */
    @ApiOperation("删除-商品分类")
    @PostMapping("/platf-remove/{itemCategId}")
    public ResultData<Object> platfRemove(@PathVariable(value = "itemCategId") Long itemCategId) {

        platfItemCategoryService.platfRemove(itemCategId);
        return ResultData.ok().Message("删除成功");

    }

    /**
     * 查询-礼物类别
     * @param platfItemCategory 分类信息
     * @return 分类列表
     */
    @ApiOperation("修改-商品分类")
    @PostMapping("/update")
    public ResultData<Object> update(@RequestBody PlatfItemCategory platfItemCategory) {

        platfItemCategoryService.updateById(platfItemCategory);
        return ResultData.ok();

    }


    /**
     * 查询商品类目api
     * @param itemCategId 商品类目id
     * @return 类目信息
     */
    @ApiOperation("查询商品类目api")
    @PostMapping("/select-item-categvo")
    public itemCategVo selectItemCategVo(@RequestParam(value = "itemCategId") Long itemCategId) {

        return platfItemCategoryService.selectItemCategVo(itemCategId);
    }

    @ApiOperation("添加分类属性")
    @PostMapping("/add-item-categ-attr")
    public ResultData<Object> addItemCategAttr(@RequestBody AddItemCategAttrRequest addItemCategAttrRequest) {
        platfItemCategoryService.addItemCategAttr(addItemCategAttrRequest);
        return ResultData.ok().Message("添加分类属性成功");
    }

    @ApiOperation("修改分类属性")
    @PostMapping("/update-item-categ-attr")
    public ResultData<Object> updateItemCategAttr(@RequestBody ItemCategAttr itemCategAttr) {
        platfItemCategoryService.updateItemCategAttr(itemCategAttr);
        return ResultData.ok().Message("修改分类属性成功");
    }

    @ApiOperation("添加分类属性值")
    @PostMapping("/add-item-categ-attr-value")
    public ResultData<Object> addItemCategAttrValue(@RequestBody AddItemCategAttrValueRequest addItemCategAttrValueRequest) {
        platfItemCategoryService.addItemCategAttrValue(addItemCategAttrValueRequest);
        return ResultData.ok().Message("添加分类属性值成功");
    }

    @ApiOperation("查询分类属性值")
    @PostMapping("/query-item-categ-attr-value")
    public ResultData<List<ItemCategAttrValueVo>> queryItemCategAttrValue(@RequestParam Long itemCategAttrId) {
        List<ItemCategAttrValueVo> itemCategAttrValueList = platfItemCategoryService.queryItemCategAttrValue(itemCategAttrId);
        return ResultData.ok(itemCategAttrValueList).Message("查询成功");
    }

    @ApiOperation("修改分类属性值")
    @PostMapping("/modified-item-categ-attr-value")
    public ResultData<Object> modifiedItemCategAttrValue(@RequestBody ItemCategAttrValueVo itemCategAttrValueVo) {
        platfItemCategoryService.modifiedItemCategAttrValue(itemCategAttrValueVo);
        return ResultData.ok().Message("修改成功");
    }

    @ApiOperation("删除分类属性")
    @PostMapping("/remove-item-categ-attr")
    public ResultData<Object> removeItemCategAttr(@RequestParam Long itemCategAttrId) {
        platfItemCategoryService.removeItemCategAttr(itemCategAttrId);
        return ResultData.ok().Message("删除分类属性成功");
    }

    @ApiOperation("删除分类属性值")
    @PostMapping("/remove-item-categ-attr-value")
    public ResultData<Object> removeItemCategAttrValue(@RequestParam Long itemAttrValueId) {
        platfItemCategoryService.removeItemCategAttrValue(itemAttrValueId);
        return ResultData.ok().Message("删除分类属性值成功");
    }


    @ApiOperation("查询分类属性")
    @PostMapping("/query-item-categ-attr-list")
    public ResultData<List<ItemCategAttrVo>> queryItemCategAttrList(@RequestParam Long platfItemCid) {
        List<ItemCategAttrVo> itemCategAttrList = platfItemCategoryService.queryItemCategAttrList(platfItemCid);
        return ResultData.ok(itemCategAttrList);
    }

    @ApiOperation("查询分类属性-ids")
    @PostMapping("/query-item-categ-attr-list-ids")
    public ResultData<List<ItemCategAttrVo>> queryItemCategAttrList(@RequestBody Long[] categIds) {
        List<ItemCategAttrVo> itemCategAttrList = platfItemCategoryService.queryItemCategAttrList(categIds);
        return ResultData.ok(itemCategAttrList);
    }

}
