package zx.vanx.shop_item.controller.frontend;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import zx.vanx.common_util.exception.ZxException;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.shop_item.request.SceCategAttrAddRequest;
import zx.vanx.shop_item.dto.SceCategAttrVo;
import zx.vanx.shop_item.request.SceCategAddRequest;
import zx.vanx.shop_item.dto.SceCategVo;
import zx.vanx.shop_item.service.PlatfSceBusiCategService;

import java.util.List;

/**
 * <p>
 * 平台的店铺经营类型分类表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
@Api(tags = "e.平台景区分类管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/shop_item/platf-sce-busi-categ")
public class PlatfSceBusiCategController {

    private final PlatfSceBusiCategService platfSceBusiCategService;

    @ApiOperation("添加景区分类")
    @PutMapping("/add-sce-categ")
    public ResultData<Object> addSceCateg(@RequestBody SceCategAddRequest sceCategAddRequest) {

        platfSceBusiCategService.addSceCateg(sceCategAddRequest);
        return ResultData.ok().Message("添加成功");
    }

    @ApiOperation("添加景区分类属性")
    @PutMapping("/add-sce-categ-property")
    public ResultData<Object> addScenicSpotClassificationProperty(@RequestBody SceCategAttrAddRequest sceCategAttrAddRequest) {

        platfSceBusiCategService.addScenicSpotClassificationProperty(sceCategAttrAddRequest);
        return ResultData.ok().Message("添加成功");

    }

    @ApiOperation("删除景区分类")
    @DeleteMapping("/remove-sce-categ")
    public ResultData<Object> removeSceCateg(@RequestBody SceCategVo sceCategVo) {

        if (ObjectUtils.isEmpty(sceCategVo.getBusinCategId())) {
            throw new ZxException(201,"businCategId不能为空");
        }

        if (ObjectUtils.isEmpty(sceCategVo.getIsLeaf())) {
            throw new ZxException(201,"isLeaf不能为空");
        }

        platfSceBusiCategService.removeSceCateg(sceCategVo);
        return ResultData.ok().Message("删除成功");

    }

    @ApiOperation("删除景区分类属性")
    @DeleteMapping("/remove-sce-categ-attr")
    public ResultData<Object> removeSceCategAttr(@RequestBody SceCategAttrVo sceCategAttrVo) {

        if (ObjectUtils.isEmpty(sceCategAttrVo.getShopCategAttrId())) {
            throw new ZxException(201,"shopCategAttrId不能为空");
        }

        if (ObjectUtils.isEmpty(sceCategAttrVo.getIsLeaf())) {
            throw new ZxException(201,"isLeaf不能为空");
        }

        platfSceBusiCategService.removeSceCategAttr(sceCategAttrVo);
        return ResultData.ok().Message("删除成功");
    }

    @ApiOperation("修改景区分类")
    @PostMapping("/modified-sce-categ")
    public ResultData<Object> modifiedSceCateg(@RequestBody SceCategVo sceCategVo) {

        if (ObjectUtils.isEmpty(sceCategVo.getBusinCategId())) {
            throw new ZxException(201,"businCategId不能为空");
        }
        platfSceBusiCategService.modifiedSceCateg(sceCategVo);
        return ResultData.ok().Message("修改成功");
    }

    @ApiOperation("修改景区分类属性")
    @PostMapping("/modified-sce-categ-attr")
    public ResultData<Object> modifiedSceCategAttr(@RequestBody SceCategAttrVo sceCategAttrVo) {

        if (ObjectUtils.isEmpty(sceCategAttrVo.getShopCategAttrId())) {
            throw new ZxException(201,"shopCategAttrId不能为空");
        }
        platfSceBusiCategService.modifiedSceCategAttr(sceCategAttrVo);
        return ResultData.ok().Message("修改成功");
    }

    @ApiOperation("查询景区分类树")
    @GetMapping("/tree")
    public ResultData<List<SceCategVo>> querySceCategTree() {
        List<SceCategVo> sceCategVoList = platfSceBusiCategService.querySceCategTree(0L);
        return ResultData.ok(sceCategVoList);
    }

    @ApiOperation("查询景区分类属性树")
    @GetMapping("/property/tree/{businCategId}")
    public ResultData<List<SceCategAttrVo>> querySceCategAttrTree(@PathVariable(value = "businCategId") Long businCategId) {

        List<SceCategAttrVo> sceCategAttrVoList = platfSceBusiCategService.querySceCategAttrTree(businCategId,0L);
        return ResultData.ok(sceCategAttrVoList);
    }
}
