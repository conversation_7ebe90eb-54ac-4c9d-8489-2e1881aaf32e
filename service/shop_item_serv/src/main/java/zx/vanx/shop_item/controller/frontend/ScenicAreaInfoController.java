package zx.vanx.shop_item.controller.frontend;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import zx.vanx.common_util.core.page.PageObj;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.shop_item.entity.ScenicAreaInfo;
import zx.vanx.shop_item.request.EvaluationPictureQueryRequest;
import zx.vanx.shop_item.request.ScenicMediaQueryRequest;
import zx.vanx.shop_item.service.ScenicAreaInfoService;
import zx.vanx.shop_item.vo.*;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
@Api(tags = "f.平台景区管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/shop_item/scenic-area-info")
public class ScenicAreaInfoController {

    private final ScenicAreaInfoService scenicAreaInfoService;

    @ApiOperation("查询景区列表填充地址信息")
    @GetMapping("/query-scenic-area-info")
    public ResultData<List<ScenicAreaInfo>> queryScenicAreaInfo() {

        List<ScenicAreaInfo> scenicAreaInfoList = scenicAreaInfoService.queryScenicAreaInfo();
        return ResultData.ok(scenicAreaInfoList).Message("查询成功");

    }

    @ApiOperation("修改地址后批量修改景区")
    @PostMapping("/batchs-update-scenic-area-info")
    public ResultData<Object> batchsUpdateScenicAreaInfo(@RequestBody List<ScenicAreaInfo> scenicAreaInfoList) {

        scenicAreaInfoService.batchsUpdateScenicAreaInfo(scenicAreaInfoList);
        return ResultData.ok().Message("修改成功");
    }

    @ApiOperation("根据当前市查询景区列表")
    @GetMapping("/query-scenic-area-info-by-city")
    public ResultData<List<ScenicAreaInfoVo>> queryScenicAreaInfoByCity(
            @RequestParam(value = "current") Long current
            ,@RequestParam(value = "limit") Long limit
            ,@RequestParam(value = "cityName",required = false) String cityName) {
        List<ScenicAreaInfoVo> scenicAreaInfoList = scenicAreaInfoService.queryScenicAreaInfoByCity(current,limit,cityName);
        return ResultData.ok(scenicAreaInfoList).Message("查询成功");
    }

    @ApiOperation("查询景区详情")
    @GetMapping("/query-scenic-detail")
    public ResultData<ScenicDetailVo> queryScenicDetail(
            @RequestParam(value = "scenicAreaId") Long scenicAreaId) {
        ScenicDetailVo scenicDetailVo = scenicAreaInfoService.queryScenicDetail(scenicAreaId);
        return ResultData.ok(scenicDetailVo).Message("查询成功");
    }

    @ApiOperation("分页查询景区视频or图片")
    @PostMapping("/query-scenic-media")
    public ResultData<PageObj<SceAreaMediaVo>> queryScenicMedia(
            @RequestBody ScenicMediaQueryRequest scenicMediaQueryRequest) {
        PageObj<SceAreaMediaVo> scenicMedia = scenicAreaInfoService.queryScenicMedia(scenicMediaQueryRequest);
        return ResultData.ok(scenicMedia).Message("查询成功");
    }

    @ApiOperation("分页查询评价晒图图片")
    @PostMapping("/query-evaluate-media")
    public ResultData<PageObj<EvaluatePictureVo>> queryEvaluateMedia(
            @RequestBody EvaluationPictureQueryRequest evaluationPictureQueryRequest) {
        PageObj<EvaluatePictureVo> scenicMedia = scenicAreaInfoService.queryEvaluateMedia(evaluationPictureQueryRequest);
        return ResultData.ok(scenicMedia).Message("查询成功");
    }

    @ApiOperation("查询景区更多详情")
    @GetMapping("/query-scenic-more-detail")
    public ResultData<List<ScenicMoreDetailVo>> queryScenicMoreDetail(
            @RequestParam(value = "scenicAreaId") Long scenicAreaId) {
        List<ScenicMoreDetailVo> scenicDetailVo = scenicAreaInfoService.queryScenicMoreDetail(scenicAreaId,0L);
        return ResultData.ok(scenicDetailVo).Message("查询成功");
    }

}
