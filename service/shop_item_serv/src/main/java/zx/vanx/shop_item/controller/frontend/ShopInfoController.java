package zx.vanx.shop_item.controller.frontend;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.exception.ZxException;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.shop_item.service.ShopInfoService;
import zx.vanx.shop_item.vo.MyShopVo;
import zx.vanx.user_permiss.entity.UserInfoFast;
import zx.vanx.user_permiss.redis.service.UserInfoFastRedisService;

import java.util.List;

/**
 * <p>
 * 系统店铺基本信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
@Api(tags = "g.系统店铺-管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/shop_item/shop-info")
public class ShopInfoController  {

    private final ShopInfoService shopInfoService;

    private final UserInfoFastRedisService userInfoFastRedisService;

    @ApiOperation("查询我的店铺")
    @GetMapping("/my-shop")
    
    public ResultData<MyShopVo> queryMyShop() {

        UserInfoFast userInfoFast = userInfoFastRedisService.getUserInfoFastByUserId(LoginHelper.getUserId());
        List<String> currentlyActiveRoles = userInfoFast.getCurrentlyActiveRole();
        if (!currentlyActiveRoles.contains("普通卖家用户")) {
            throw new ZxException(207, "普通买家用户无店铺信息，请切换身份或成为卖家");
        }

        MyShopVo myShopVo = shopInfoService.queryMyShop(LoginHelper.getUserId());
        return ResultData.ok(myShopVo).Message("查询成功");

    }

}

