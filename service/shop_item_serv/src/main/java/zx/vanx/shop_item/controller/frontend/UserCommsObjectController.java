package zx.vanx.shop_item.controller.frontend;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.shop_item.entity.UserCommsObject;
import zx.vanx.shop_item.request.UserCommsRequest;
import zx.vanx.shop_item.service.UserCommsObjectService;
import zx.vanx.shop_item.vo.UserCommsVo;

import java.util.List;

/**
 * <p>
 * 系统对象（店铺、商品、景区）的用户的评论表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@Api(tags = "（店铺、商品、景区）评论")
@RestController
@RequiredArgsConstructor
@RequestMapping("/shop_item/user-comms-object")
public class UserCommsObjectController  {


    private final UserCommsObjectService userCommsObjectService;

    @ApiOperation("添加用户评论")
    @PostMapping("/add-user-comms")
    
    public ResultData<UserCommsObject> addUserComms(@RequestBody UserCommsRequest userCommsRequest) {

        UserCommsObject sysObjectComms = userCommsObjectService.addUserComms(LoginHelper.getUserId(),userCommsRequest);

        // 添加评论
        return ResultData.ok(sysObjectComms);
    }

    @ApiOperation("删除用户评论")
    @PostMapping("/delete-user-comms")
    public ResultData<Object> deleteUserComms(@RequestBody UserCommsVo userCommsVo) {
        userCommsObjectService.deleteUserComms(userCommsVo);
        return ResultData.ok().Message("删除成功");
    }

    @ApiOperation("查询用户评论")
    @PostMapping("/select-user-comms/{commsedObjectId}")
    public ResultData<List<UserCommsVo>> selectUserComms(@PathVariable("commsedObjectId") Long commsedObjectId) {

        List<UserCommsVo> sysObjectComms = userCommsObjectService.selectUserComms(commsedObjectId);
        return ResultData.ok(sysObjectComms);
    }

    @ApiOperation("查询用户子评论")
    @PostMapping("/select-sub-user-comms/{topCommsId}")
    public ResultData<List<UserCommsVo>> selectSubUserComms(@PathVariable("topCommsId") Long topCommsId) {

        List<UserCommsVo> sysObjectComms = userCommsObjectService.selectSubUserComms(topCommsId);
        return ResultData.ok(sysObjectComms);

    }

}

