package zx.vanx.shop_item.controller.internal;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import zx.vanx.shop_item.service.ItemInfoService;
import zx.vanx.shop_item.vo.ActivityItemVo;

import java.util.List;

@Api(tags = "内部接口-店铺信息")
@RestController
@RequiredArgsConstructor
@RequestMapping("/shop_item/item-info-internal")
public class ItemInfoInternalController {

    private final ItemInfoService itemInfoService;

    @ApiOperation("查询活动商品列表")
    @PostMapping("/activity-item-list")
    public List<ActivityItemVo> queryActivityItemList(@RequestBody List<Long> skuIds) {
        return itemInfoService.queryActivityItemList(skuIds);
    }

    @ApiOperation("查询活动商品详情")
    @PostMapping("/activity-item-detail")
    public ActivityItemVo queryActivityItemDetail(@RequestParam Long itemId,@RequestParam Long userId) {
        return itemInfoService.queryActivityItemDetail(itemId,userId);
    }
}