package zx.vanx.shop_item.controller.internal;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import zx.vanx.shop_item.entity.ShopInfo;
import zx.vanx.shop_item.service.ShopInfoService;

@Api(tags = "内部接口-店铺信息")
@RestController
@RequiredArgsConstructor
@RequestMapping("/shop_item/shop-info-internal")
public class ShopInfoInternalController {

    private final ShopInfoService shopInfoService;

    @ApiOperation("查询店铺基本信息")
    @GetMapping("/shop-info")
    public ShopInfo queryShopInfo(@RequestParam("shopId") Long shopId) {
        return shopInfoService.queryShopInfo(shopId);
    }

}
