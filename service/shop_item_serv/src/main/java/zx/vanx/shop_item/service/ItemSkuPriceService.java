package zx.vanx.shop_item.service;

import zx.vanx.shop_item.entity.ItemSkuPrice;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * vanx_item_sku_price 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
public interface ItemSkuPriceService extends IService<ItemSkuPrice> {

    /**
     * 通过商品id查询商品价格
     * @param itemId 商品id
     * @return 商品价格
     */
    ItemSkuPrice selectOneByItemId(Long itemId);

}
