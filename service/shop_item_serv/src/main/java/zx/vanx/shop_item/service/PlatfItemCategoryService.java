package zx.vanx.shop_item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import zx.vanx.common_util.core.page.PageObj;
import zx.vanx.shop_item.entity.ItemCategAttr;
import zx.vanx.shop_item.entity.PlatfItemCategory;
import zx.vanx.shop_item.request.AddItemCategAttrRequest;
import zx.vanx.shop_item.request.AddItemCategAttrValueRequest;
import zx.vanx.shop_item.request.SendGiftRequest;
import zx.vanx.shop_item.vo.ItemCategAttrValueVo;
import zx.vanx.shop_item.vo.ItemCategAttrVo;
import zx.vanx.shop_item.vo.itemCategVo;

import java.util.List;

/**
 * <p>
 * vanx_platf_item_category 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
public interface PlatfItemCategoryService extends IService<PlatfItemCategory> {

    /**
     * 查询-分类列表tree
     * @return 分类列表
     */
    List<PlatfItemCategory> tree();

    /**
     * 删除平台分类列表
     * @param itemCategId 分类id
     */
    void platfRemove(Long itemCategId);

    /**
     * 查询-分类列表
     * @param itemCategId 分类id
     * @param page 页码
     * @param limit 每页数量
     * @return 分类列表
     */
    PageObj<PlatfItemCategory> list(Long itemCategId, Long page, Long limit);

    /**
     * 查询-礼物类别列表
     * @param itemCategName 分类名称
     * @return 分类列表
     */
    List<PlatfItemCategory> queryItemCategoryList(String itemCategName);

    /**
     * 查询-礼物类别
     * @param itemCategName 分类名称
     * @return 分类列表
     */
    PlatfItemCategory queryGiftCategoryOne(String itemCategName);

    /**
     * 修改启用状态
     * @param platfItemCategory 分类信息
     */
    void updateGiftCategoryStatus(PlatfItemCategory platfItemCategory);

    /**
     * 查询-分类对象带children
     * @param itemCategName 分类名称
     * @return 分类对象
     */
    PlatfItemCategory queryGiftCategory(String itemCategName);

    /**
     * 查询-礼物类别列表
     * @param parentCategId 父级分类id
     * @return 分类列表
     */
    List<PlatfItemCategory> queryGiftCategoryListByParentId(Long parentCategId);

    /**
     * 查询-商品分类-tree（所有）
     * @return 分类列表
     */
    List<PlatfItemCategory> treeAll();

    /**
     * 查询商品类目api
     * @param itemCategId 商品类目id
     * @return 类目信息
     */
    itemCategVo selectItemCategVo(Long itemCategId);

    /**
     * 用户赠送礼物
     * @param userId 用户id
     * @param sendGiftRequest 赠送礼物请求
     */
    void sendGift(Long userId,SendGiftRequest sendGiftRequest);

    /**
     * 添加分类属性
     * @param addItemCategAttrRequest 添加分类属性请求
     */
    void addItemCategAttr(AddItemCategAttrRequest addItemCategAttrRequest);

    /**
     * 添加分类属性值
     * @param addItemCategAttrValueRequest 添加分类属性值请求
     */
    void addItemCategAttrValue(AddItemCategAttrValueRequest addItemCategAttrValueRequest);

    /**
     * 删除分类属性
     * @param itemCategAttrId 分类属性id
     */
    void removeItemCategAttr(Long itemCategAttrId);

    /**
     * 删除分类属性值
     * @param itemAttrValueId 分类属性值id
     */
    void removeItemCategAttrValue(Long itemAttrValueId);

    /**
     * 查询分类属性
     * @param platfItemCid 分类id
     * @return 分类属性列表
     */
    List<ItemCategAttrVo> queryItemCategAttrList(Long platfItemCid);

    /**
     * 修改分类属性
     * @param itemCategAttr 分类属性
     */
    void updateItemCategAttr(ItemCategAttr itemCategAttr);

    /**
     * 查询分类属性列表
     * @param categIds 分类id列表
     * @return 分类属性列表
     */
    List<ItemCategAttrVo> queryItemCategAttrList(Long[] categIds);

    /**
     * 查询分类属性值列表
     * @param itemCategAttrId 分类属性id
     * @return 分类属性值列表
     */
    List<ItemCategAttrValueVo> queryItemCategAttrValue(Long itemCategAttrId);

    /**
     * 修改分类属性值
     * @param itemCategAttrValueVo 分类属性值
     */
    void modifiedItemCategAttrValue(ItemCategAttrValueVo itemCategAttrValueVo);

    /**
     * 查询-指定商品分类-tree
     * @param itemCategName 商品分类名称
     * @return 商品分类-tree
     */
    List<PlatfItemCategory> specifyItemCategoryTree(String itemCategName);
}
