package zx.vanx.shop_item.service;

import zx.vanx.shop_item.request.SceCategAttrAddRequest;
import zx.vanx.shop_item.dto.SceCategAttrVo;
import zx.vanx.shop_item.request.SceCategAddRequest;
import zx.vanx.shop_item.dto.SceCategVo;
import zx.vanx.shop_item.entity.PlatfSceBusiCateg;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 平台的店铺经营类型分类表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
public interface PlatfSceBusiCategService extends IService<PlatfSceBusiCateg> {

    /**
     * 查询景区分类列表
     * @return 景区分类列表
     */
    List<SceCategVo> querySceCategTree(Long parentCategId);

    /**
     * 添加景区分类属性
     * @param sceCategAttrAddRequest 景区分类属性
     */
    void addScenicSpotClassificationProperty(SceCategAttrAddRequest sceCategAttrAddRequest);

    /**
     * 查询景区分类属性树
     * @param businCategId 景区分类ID
     * @return 景区分类属性树
     */
    List<SceCategAttrVo> querySceCategAttrTree(Long businCategId,Long parentAttributesId);

    /**
     * 添加景区分类
     * @param sceCategAddRequest 景区分类
     */
    void addSceCateg(SceCategAddRequest sceCategAddRequest);

    /**
     * 删除景区分类
     * @param sceCategVo 景区分类
     */
    void removeSceCateg(SceCategVo sceCategVo);

    /**
     * 删除景区分类属性
     * @param sceCategAttrVo 景区分类属性
     */
    void removeSceCategAttr(SceCategAttrVo sceCategAttrVo);

    /**
     * 修改景区分类
     * @param sceCategAttrVo 景区分类属性
     */
    void modifiedSceCategAttr(SceCategAttrVo sceCategAttrVo);

    /**
     * 修改景区分类
     * @param sceCategVo 景区分类
     */
    void modifiedSceCateg(SceCategVo sceCategVo);
}
