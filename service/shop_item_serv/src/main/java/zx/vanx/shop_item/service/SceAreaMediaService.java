package zx.vanx.shop_item.service;

import zx.vanx.shop_item.entity.SceAreaMedia;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
public interface SceAreaMediaService extends IService<SceAreaMedia> {

    /**
     * 添加景区图片列表
     * @param scenicAreaId 景区id
     * @param scenicPhoto 景区图片String列表
     */
    void insertScenicPictureList(Long scenicAreaId, String scenicPhoto);

    /**
     * 查询景区封面url
     * @param scenicAreaId 景区id
     * @return 景区封面url
     */
    String selectSecnicCover(Long scenicAreaId);

    /**
     * 查询景区图片列表
     * @param scenicAreaId 景区id
     * @return 景区图片列表
     */
    List<String> selectSecnicPictures(Long scenicAreaId);
}
