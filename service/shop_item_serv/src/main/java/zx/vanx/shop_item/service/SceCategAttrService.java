package zx.vanx.shop_item.service;

import zx.vanx.shop_item.dto.SceCategAttrVo;
import zx.vanx.shop_item.entity.SceAreaAttrValue;
import zx.vanx.shop_item.entity.SceCategAttr;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
public interface SceCategAttrService extends IService<SceCategAttr> {

    /**
     * 查询景区分类属性树
     * @param businCategId 景区id
     * @return 景区分类属性树
     */
    List<SceCategAttrVo> querySceCategAttrTree(Long businCategId,Long parentAttributesId);

    /**
     * 查询第一级属性
     * @param businCategId 景区id
     * @param parentAttributesId 父id
     * @return 第一级属性列表
     */
    List<SceCategAttrVo> querySceCategAttrByBusinCategIdAndParentAttributesId(Long businCategId, Long parentAttributesId);

    /**
     * 根据景区属性名称查询景区属性
     * @param attrName 景区属性名称
     * @return SceCategAttr
     */
    SceCategAttr selectOneScenicAreaAttrByAttrName(String attrName);

    /**
     *
     * @param scenicAreaId 景区id
     * @param attrName 属性名
     * @param attrValue 属性值
     */
    void addSecCategAttrValue(Long scenicAreaId, String attrName,String attrValue);

    /**
     * 根据景区id和景区属性名称查询景区属性值
     * @param scenicAreaId 景区id
     * @param attrName 景区属性名称
     * @return 景区属性值
     */
    SceAreaAttrValue selectCategAttrValue(Long scenicAreaId, String attrName);
}
