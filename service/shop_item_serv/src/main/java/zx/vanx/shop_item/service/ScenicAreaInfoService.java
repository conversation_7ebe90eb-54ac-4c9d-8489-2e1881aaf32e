package zx.vanx.shop_item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import zx.vanx.common_util.core.page.PageObj;
import zx.vanx.shop_item.entity.ScenicAreaInfo;
import zx.vanx.shop_item.request.EvaluationPictureQueryRequest;
import zx.vanx.shop_item.request.ScenicMediaQueryRequest;
import zx.vanx.shop_item.vo.*;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
public interface ScenicAreaInfoService extends IService<ScenicAreaInfo> {

    /**
     * 根据景区名称查询景区信息
     * @param scenicName 景区名称
     * @return 景区信息
     */
    ScenicAreaInfo selectOneByScenicName(String scenicName);


    /**
     * 查询景区列表填充地址信息
     * @return 景区列表
     */
    List<ScenicAreaInfo> queryScenicAreaInfo();

    /**
     * 修改地址后批量修改景区
     * @param scenicAreaInfoList 景区列表
     */
    void batchsUpdateScenicAreaInfo(List<ScenicAreaInfo> scenicAreaInfoList);

    /**
     * 根据当前市查询景区列表
     * @param current 当前页
     * @param limit 每页记录数
     * @param cityName 当前市
     * @return 景区列表
     */
    List<ScenicAreaInfoVo> queryScenicAreaInfoByCity(Long current,Long limit,String cityName);

    /**
     * 查询景区详情
     * @param scenicAreaId 景区id
     * @return 景区详情
     */
    ScenicDetailVo queryScenicDetail(Long scenicAreaId);

    /**
     * 分页查询景区视频or图片
     * @param scenicMediaQueryRequest 查询条件
     * @return 分页查询结果
     */
    PageObj<SceAreaMediaVo> queryScenicMedia(ScenicMediaQueryRequest scenicMediaQueryRequest);

    /**
     * 分页查询评价晒图图片
     * @param evaluationPictureQueryRequest 查询条件
     * @return 分页查询结果
     */
    PageObj<EvaluatePictureVo> queryEvaluateMedia(EvaluationPictureQueryRequest evaluationPictureQueryRequest);

    /**
     * 查询景区更多详情
     * @param scenicAreaId 景区id
     * @param parentAttributesId 景区id
     * @return 景区更多详情
     */
    List<ScenicMoreDetailVo> queryScenicMoreDetail(Long scenicAreaId,Long parentAttributesId);
}
