package zx.vanx.shop_item.service;

import zx.vanx.shop_item.entity.UserCommsObject;
import com.baomidou.mybatisplus.extension.service.IService;
import zx.vanx.shop_item.request.UserCommsRequest;
import zx.vanx.shop_item.vo.UserCommsVo;

import java.util.List;

/**
 * <p>
 * 系统对象（店铺、商品、景区）的用户的评论表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
public interface UserCommsObjectService extends IService<UserCommsObject> {

    /**
     * 添加用户评论
     * @param userId 用户id
     * @param userCommsRequest 用户评论请求
     * @return 用户评论对象
     */
    UserCommsObject addUserComms(Long userId,UserCommsRequest userCommsRequest);

    /**
     * 删除用户评论
     * @param userCommsVo 评论id
     */
    void deleteUserComms(UserCommsVo userCommsVo);

    /**
     * 查询用户评论
     * @param commsedObjectId 评论对象id
     * @return 用户评论列表
     */
    List<UserCommsVo> selectUserComms(Long commsedObjectId);

    /**
     * 查询用户子评论
     * @param topCommsId 父评论id
     * @return 用户子评论列表
     */
    List<UserCommsVo> selectSubUserComms(Long topCommsId);
}
