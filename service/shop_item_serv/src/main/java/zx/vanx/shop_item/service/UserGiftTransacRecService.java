package zx.vanx.shop_item.service;

import zx.vanx.shop_item.entity.UserGiftTransacRec;
import com.baomidou.mybatisplus.extension.service.IService;
import zx.vanx.shop_item.request.GiftIncomeRequest;
import zx.vanx.shop_item.request.GiftSendQueryRequest;
import zx.vanx.shop_item.request.SendGiftRequest;
import zx.vanx.shop_item.request.SingleWorksGiftIncomeRequest;
import zx.vanx.shop_item.vo.GiftIncomeVo;
import zx.vanx.vo.IncomeAndExpenditureDetailVo;

import java.util.List;

/**
 * <p>
 * 用户间礼物交易记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-10
 */
public interface UserGiftTransacRecService extends IService<UserGiftTransacRec> {

    /**
     * 添加用户礼物关系表逻辑
     * @param sendGiftRequest 发送礼物请求
     */
    void addUserGiftRelation(SendGiftRequest sendGiftRequest);

    /**
     * 查询-礼物收入列表
     * @param userId 用户id
     * @param giftIncomeRequest 礼物收入请求
     * @return 礼物收入列表
     */
    List<GiftIncomeVo> queryGiftIncomeList(Long userId, GiftIncomeRequest giftIncomeRequest);

    /**
     * 查询-礼物赠送列表
     * @param giftSendQueryRequest 礼物赠送查询请求
     * @return 礼物赠送列表
     */
    List<IncomeAndExpenditureDetailVo<GiftIncomeVo>> queryGiftSenderList(GiftSendQueryRequest giftSendQueryRequest);

    /**
     * 查询-单个作品-礼物收入列表
     * @param userId 用户id
     * @param singleWorksGiftIncomeRequest 单个作品-礼物收入请求
     * @return 单个作品-礼物收入列表
     */
    List<GiftIncomeVo> querySingleWorksGiftIncomeList(Long userId, SingleWorksGiftIncomeRequest singleWorksGiftIncomeRequest);
}
