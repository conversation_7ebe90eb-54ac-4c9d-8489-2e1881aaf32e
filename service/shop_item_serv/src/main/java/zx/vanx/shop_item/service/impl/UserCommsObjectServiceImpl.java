package zx.vanx.shop_item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import zx.vanx.common_util.core.redis.RedisCache;
import zx.vanx.common_util.utils.BeanUtil;
import zx.vanx.service.MinioService;
import zx.vanx.shop_item.entity.UserCommsMedia;
import zx.vanx.shop_item.entity.UserCommsObject;
import zx.vanx.shop_item.mapper.UserCommsObjectMapper;
import zx.vanx.shop_item.request.UserCommsPictureRequest;
import zx.vanx.shop_item.request.UserCommsRequest;
import zx.vanx.shop_item.service.UserCommsMediaService;
import zx.vanx.shop_item.service.UserCommsObjectService;
import zx.vanx.shop_item.vo.UserCommsPictureVo;
import zx.vanx.shop_item.vo.UserCommsVo;
import zx.vanx.user_permiss.redis.key.PermissKey;
import zx.vanx.user_permiss.vo.UserDetail;

import java.util.List;

/**
 * <p>
 * 系统对象（店铺、商品、景区）的用户的评论表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class UserCommsObjectServiceImpl extends ServiceImpl<UserCommsObjectMapper, UserCommsObject> implements UserCommsObjectService {

    private final RedisCache redisCache;

    private final UserCommsMediaService userCommsMediaService;

    private final MinioService minioService;

    /**
     * 添加用户评论
     * @param userId 用户id
     * @param userCommsRequest 用户评论请求
     * @return 用户评论对象
     */
    @Override
    public UserCommsObject addUserComms(Long userId,UserCommsRequest userCommsRequest) {

        // 对象拷贝
        UserCommsObject userCommsObject = BeanUtil.copyProperties(userCommsRequest, UserCommsObject::new);
        userCommsObject.setUserId(userId);

        // 添加到数据库
        baseMapper.insert(userCommsObject);

        // 添加评论图片
        List<UserCommsPictureRequest> commsPictureList = userCommsRequest.getCommsPictureList();

        if (!CollectionUtils.isEmpty(commsPictureList)) {
            for (UserCommsPictureRequest userCommsPictureRequest : commsPictureList) {
                UserCommsMedia userCommsMedia = BeanUtil.copyProperties(userCommsPictureRequest, UserCommsMedia::new);
                userCommsMedia.setObjectCommsId(userCommsObject.getObjectCommsId());
                userCommsMediaService.save(userCommsMedia);
            }
        }

        return userCommsObject;

    }

    /**
     * 删除用户评论
     * @param userCommsVo 评论对象
     */
    @Override
    public void deleteUserComms(UserCommsVo userCommsVo) {

        // 删除评论
        baseMapper.deleteById(userCommsVo.getObjectCommsId());

        // 删除评论图片
        List<UserCommsPictureVo> userCommsPictureList = userCommsVo.getUserCommsPictureList();

        if (!CollectionUtils.isEmpty(userCommsPictureList)) {

            for (UserCommsPictureVo userCommsPictureVo : userCommsPictureList) {
                // 删除评论图片
                userCommsMediaService.removeById(userCommsPictureVo.getCommsMediaId());

                // 删除评论图片文件
                minioService.removeObject(userCommsPictureVo.getMediaMinioName());
            }
        }

        // 查询子评论
        List<UserCommsObject> subUserCommsList = baseMapper.selectList((new QueryWrapper<UserCommsObject>()
                .eq("parent_comms_id", userCommsVo.getObjectCommsId())));
        // 递归删除子评论
        if (!CollectionUtils.isEmpty(subUserCommsList)) {
            for (UserCommsObject subUserComms : subUserCommsList) {
                deleteUserComms(BeanUtil.copyProperties(subUserComms, UserCommsVo::new));
            }
        }
    }

    /**
     * 查询用户评论
     * @param commsedObjectId 评论对象id
     * @return 用户评论列表
     */
    @Override
    public List<UserCommsVo> selectUserComms(Long commsedObjectId) {
        // 查询视频一级评论
        List<UserCommsObject> userCommsObjectList = baseMapper.selectList((new QueryWrapper<UserCommsObject>()
                .eq("commsed_object_id", commsedObjectId))
                .eq("top_comms_id",0)
                .orderByDesc("created_time"));

        List<UserCommsVo>  userCommsVoList = BeanUtil.copyList(userCommsObjectList, UserCommsVo.class);

        if (!CollectionUtils.isEmpty(userCommsVoList)) {

            for (UserCommsVo userCommsVo : userCommsVoList) {

                //填充孩子
                List<UserCommsObject> subUserCommsList = baseMapper.selectList((new QueryWrapper<UserCommsObject>()
                        .eq("top_comms_id",userCommsVo.getObjectCommsId())
                        .orderByDesc("created_time")));

                // 填充各自评论名称或头像
                for (UserCommsObject userCommsObject : subUserCommsList) {

                    UserDetail subUserDetail = redisCache.getCacheObject(PermissKey.USER_DETAIL + userCommsObject.getParentCommsUserId());
                    userCommsObject.setParentCommsUserName(subUserDetail.getUserInfo().getUserNickname());
                    UserDetail subUserDetail2 = redisCache.getCacheObject(PermissKey.USER_DETAIL + userCommsObject.getUserId());
                    userCommsObject.setUserNickName(subUserDetail2.getUserInfo().getUserNickname());
                    userCommsObject.setUserPhoto(subUserDetail2.getUserAvatar().getPictureUrl());
                }

                userCommsVo.setSecondCommsList(BeanUtil.copyList(subUserCommsList, UserCommsVo.class));

                //填充评论用户昵称和用户头像
                UserDetail userDetail = redisCache.getCacheObject(PermissKey.USER_DETAIL + userCommsVo.getUserId());
                userCommsVo.setUserNickName(userDetail.getUserInfo().getUserNickname());
                userCommsVo.setUserPhoto(userDetail.getUserAvatar().getPictureUrl());

                // 填充评论图片
                List<UserCommsMedia> userCommsMediaList = userCommsMediaService.list((new QueryWrapper<UserCommsMedia>().eq("object_comms_id", userCommsVo.getObjectCommsId())));
                if (!CollectionUtils.isEmpty(userCommsMediaList)) {
                    userCommsVo.setUserCommsPictureList(BeanUtil.copyList(userCommsMediaList, UserCommsPictureVo.class));
                }

            }
        }
        return userCommsVoList;
    }

    /**
     * 查询用户子评论
     * @param topCommsId 父评论id
     * @return 用户子评论列表
     */
    @Override
    public List<UserCommsVo> selectSubUserComms(Long topCommsId) {

        List<UserCommsObject> subUserCommsList = baseMapper.selectList((new QueryWrapper<UserCommsObject>()
                .eq("top_comms_id",topCommsId))
                .orderByDesc("created_time"));

        List<UserCommsVo>  subUserCommsVoList = BeanUtil.copyList(subUserCommsList, UserCommsVo.class);

        if (!CollectionUtils.isEmpty(subUserCommsVoList)) {
            for (UserCommsVo subUserCommsVo : subUserCommsVoList) {

                UserDetail subUserDetail = redisCache.getCacheObject(PermissKey.USER_DETAIL + subUserCommsVo.getParentCommsUserId());
                subUserCommsVo.setParentCommsUserName(subUserDetail.getUserInfo().getUserNickname());

                UserDetail subUserDetail2 = redisCache.getCacheObject(PermissKey.USER_DETAIL + subUserCommsVo.getUserId());
                subUserCommsVo.setUserNickName(subUserDetail2.getUserInfo().getUserNickname());
                subUserCommsVo.setUserPhoto(subUserDetail2.getUserAvatar().getPictureUrl());

                // 填充评论图片
                List<UserCommsMedia> userCommsMediaList = userCommsMediaService.list((new QueryWrapper<UserCommsMedia>().eq("object_comms_id", subUserCommsVo.getObjectCommsId())));
                if (!CollectionUtils.isEmpty(userCommsMediaList)) {
                    subUserCommsVo.setUserCommsPictureList(BeanUtil.copyList(userCommsMediaList, UserCommsPictureVo.class));
                }

            }
        }

        return subUserCommsVoList;
    }
}
