server:
  port: 9084
spring:
  application:
    name: shop-item

--- # nacos
spring:
  cloud:
    nacos:
      discovery:
        server-addr: **************:8848
        ip: **************
        heart-beat-interval: 5000
        heart-beat-timeout: 15000
        ip-delete-timeout: 30000
        register-enabled: true
        cluster-name: DEFAULT
        group: DEFAULT_GROUP
        namespace: public
        naming-load-cache-at-start: true
        watch-delay: 5000
# 数据库
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************
    password: AsWPoL236
    username: root
#设置远程调用链接超时时间和读取超时时间
feign:
  client:
    config:
      default:
        connect-timeout: 5000
        read-timeout: 20000
--- # redis
spring:
  redis:
    host: **************
    port: 6379
    password: zx2023
    database: 1
    timeout: 1800000