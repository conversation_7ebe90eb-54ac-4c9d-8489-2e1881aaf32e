<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.shop_item.mapper.ItemInfoMapper">

    <select id="selectItemList" resultType="zx.vanx.shop_item.vo.ItemInfoVo">
            SELECT DISTINCT
            vii.item_id,
            vii.item_name,
            vii.recommend_value,
            vii.is_points_exchange,
            vii.platform_first_cid,
            vii.platform_second_cid,
            (SELECT item_categ_name FROM vanx_platf_item_category WHERE item_categ_id = vii.platform_first_cid) AS platformFirstCatogName,
            (SELECT item_categ_name FROM vanx_platf_item_category WHERE item_categ_id = vii.platform_second_cid) AS platformSecondCatogName,
            vii.item_sub_name,
            vii.item_advert,
            vii.add_source,
            vii.operator,
            vii.product_code,
            vii.has_price,
            vii.is_sale,
            vis.item_sku_id,
            visp.currency_type,
            sub_query.sell_price,
            sub_query.pic_url,
            vii.created_time
            FROM vanx_item_sku vis
            LEFT JOIN vanx_item_info vii ON vii.item_id = vis.item_id
            LEFT JOIN vanx_item_sku_price visp ON vis.item_sku_id = visp.sku_id
            LEFT JOIN (
            SELECT vispc.sku_id, MAX(visp.sell_price) AS sell_price, MAX(vispc.pic_url) AS pic_url
            FROM vanx_item_sku_price visp
            LEFT JOIN vanx_item_sku_picture vispc ON visp.sku_id = vispc.sku_id
            GROUP BY vispc.sku_id
            ) AS sub_query ON vis.item_sku_id = sub_query.sku_id

        <where>
            <if test="itemCategLevel == 1 and itemCategId != null">
                and vii.platform_first_cid = #{itemCategId}
            </if>
            <if test="itemCategLevel == 2 and itemCategId != null">
               and vii.platform_second_cid = #{itemCategId}
            </if>
            <if test="itemInfoStatus != null and itemInfoStatus != ''">
               and vii.item_info_status = #{itemInfoStatus}
            </if>

            AND vis.is_default = 1
            AND vii.platform_first_cid NOT IN (
            SELECT item_categ_id FROM vanx_platf_item_category WHERE item_categ_name = '礼物'
            )
            AND vii.is_deleted = FALSE
        </where>

        order by vii.created_time desc;

    </select>

    <!--查询礼物列表-->
    <select id="selectGiftList" resultType="zx.vanx.shop_item.vo.GiftVo">

        select
        vii.item_id,
        vii.item_name,
        vii.recommend_value,
        vii.item_info_status,
        vii.platform_first_cid,
        vii.platform_second_cid,
        (select item_categ_name from vanx_platf_item_category where item_categ_id = vii.platform_first_cid)
        as platformFirstCatogName,
        (select item_categ_name from vanx_platf_item_category where item_categ_id = vii.platform_second_cid)
        as platformSecondCatogName,
        vii.is_sale,
        visc.sku_id,
        visc.item_carto_id,
        visc.pic_name,
        visc.pic_url,
        visc.play_json_file_name,
        visc.play_json_file_url,
        visc.play_git_file_name,
        visc.play_git_file_url,
        visc.is_used,
        visp.currency_type,
        visp.sell_price
        from vanx_item_info vii
        left join vanx_item_sku vis on vii.item_id = vis.item_id
        left join vanx_item_sku_cartoon visc on vii.item_id = visc.item_id
        left join vanx_item_sku_price visp on vii.item_id = visp.item_id

        <where>
            <if test="itemCategLevel == 1 and itemCategId != null">
               and vii.platform_first_cid = #{itemCategId}
            </if>
            <if test="itemCategLevel == 2 and itemCategId != null">
               and vii.platform_second_cid = #{itemCategId}
            </if>

            <if test="itemInfoStatus != '' and itemInfoStatus != null">
                and vii.item_info_status = #{itemInfoStatus}
            </if>

            <if test="itemName != '' and itemName != null">
                and vii.item_name like #{itemName}
            </if>

            and visc.is_used = true
            and vii.is_deleted = false
        </where>
        order by visp.sell_price
    </select>

    <select id="selectAppGiftList" resultType="zx.vanx.shop_item.vo.GiftVo">

        select
        vii.item_id,
        vii.item_name,
        vii.recommend_value,
        vii.item_info_status,
        vii.platform_first_cid,
        vii.platform_second_cid,
        (select item_categ_name from vanx_platf_item_category where item_categ_id = vii.platform_first_cid)
        as platformFirstCatogName,
        (select item_categ_name from vanx_platf_item_category where item_categ_id = vii.platform_second_cid)
        as platformSecondCatogName,
        vii.is_sale,
        visc.sku_id,
        visc.item_carto_id,
        visc.pic_name,
        visc.pic_url,
        visc.play_json_file_name,
        visc.play_json_file_url,
        visc.play_git_file_name,
        visc.play_git_file_url,
        visc.is_used,
        visp.currency_type,
        visp.sell_price
        from vanx_item_info vii
        left join vanx_item_sku vis on vii.item_id = vis.item_id
        left join vanx_item_sku_cartoon visc on vii.item_id = visc.item_id
        left join vanx_item_sku_price visp on vii.item_id = visp.item_id
        <where>
            vii.item_info_status = '在售'
            and vii.platform_first_cid = (SELECT item_categ_id FROM `vanx_platf_item_category` where item_categ_name = #{itemCategName})
            and visc.is_used = true
            and vii.is_deleted = false
        </where>
        order by visp.sell_price
    </select>

    <!--查询虚拟币充值列表-->
    <select id="queryVirtualBRechargeList" resultType="zx.vanx.shop_item.vo.virtual.VirtualB">
        select
        vii.item_name,
        visp.sell_price
        from vanx_item_info vii
        left join vanx_item_sku vis on vii.item_id = vis.item_id
        left join vanx_item_sku_picture vispc on vis.item_sku_id = vispc.sku_id
        left join vanx_item_sku_price visp on vii.item_id = visp.item_id
        <where>
            vii.item_info_status = '在售'
            and vii.platform_second_cid = (SELECT item_categ_id FROM `vanx_platf_item_category` where item_categ_name = '虚拟币充值')
            and vii.is_deleted = false
        </where>

    </select>

    <!--查询活动商品列表映射map-->
    <resultMap id="ActivityItemVoMap" type="zx.vanx.shop_item.vo.ActivityItemVo">
        <id column="item_id" property="itemId" />
        <id column="shop_id" property="shopId" />
        <result column="item_name" property="itemName" />
        <result column="platform_first_cid" property="platformFirstCid" />
        <result column="platform_second_cid" property="platformSecondCid" />
        <result column="platformFirstCatogName" property="platformFirstCatogName" />
        <result column="platformSecondCatogName" property="platformSecondCatogName" />
        <result column="item_advert" property="itemAdvert" />
        <result column="product_code" property="productCode" />
        <result column="is_sale" property="isSale" />

        <!-- 嵌套的ItemSkuVo映射 -->
        <collection property="itemSkuList" ofType="zx.vanx.shop_item.vo.ItemSkuVo">
            <id column="item_sku_id" property="itemSkuId" />
            <result column="item_sku_name" property="itemSkuName" />
            <result column="sell_price" property="sellPrice" />
            <result column="inventory" property="inventory" />
            <result column="currency_type" property="currencyType" />
            <!-- 其他字段的映射 -->

            <!-- 嵌套的ItemSkuPictureVo映射 -->
            <collection property="itemSkuPictureList" ofType="zx.vanx.shop_item.vo.ItemSkuPictureVo">
                <id column="sku_pic_id" property="skuPicId" />
                <result column="sku_id" property="skuId" />
                <result column="pic_name" property="picName" />
                <result column="skuPicUrl" property="skuPicUrl" />
                <!-- 其他字段的映射 -->
            </collection>

            <!-- 嵌套的ItemAttrValueVo映射 -->
            <collection property="itemAttrValueList" ofType="zx.vanx.shop_item.vo.ItemAttrValueVo">
                <id column="item_attr_value_id" property="itemAttrValueId" />
                <result column="is_added_picture" property="isAddedPicture" />
                <result column="pic_name" property="picName" />
                <result column="valuePicUrl" property="valuePicUrl" />
                <result column="attr_name" property="attrName" />
                <result column="attr_value_str" property="attrValueStr" />
                <!-- 其他字段的映射 -->
            </collection>

        </collection>

    </resultMap>

    <select id="selectActivityItemList" resultMap="ActivityItemVoMap">
        select
            vii.item_id,
            vii.shop_id,
            vii.item_name,
            vii.platform_first_cid,
            vii.platform_second_cid,
            (select item_categ_name from vanx_platf_item_category where item_categ_id = vii.platform_first_cid)
                as platformFirstCatogName,
            (select item_categ_name from vanx_platf_item_category where item_categ_id = vii.platform_second_cid)
                as platformSecondCatogName,
            vii.item_advert,
            vii.product_code,
            vii.is_sale,
            vis.item_sku_id,
            vis.item_sku_name,
            vis.inventory,
            visp.currency_type,
            visp.sell_price,
            vispc.sku_pic_id,
            vispc.sku_id,
            vispc.pic_name,
            vispc.pic_url as skuPicUrl,
            vai.item_attr_value_id,   <!-- 新增字段，用于映射ItemAttrValue -->
            vai.is_added_picture, <!-- 新增字段 -->
            vai.pic_name, <!-- 新增字段 -->
            vai.pic_url as valuePicUrl, <!-- 新增字段 -->
            vai.attr_name, <!-- 新增字段 -->
            vai.attr_value_str <!-- 新增字段 -->
        from vanx_item_sku vis
                 left join vanx_item_info vii on vii.item_id = vis.item_id
                 left join vanx_item_sku_price visp on vis.item_sku_id = visp.sku_id
                 left join vanx_item_sku_picture vispc on vis.item_sku_id = vispc.sku_id
                left join vanx_item_attr_value vai on vai.item_id = vii.item_id and vai.sku_id = vis.item_sku_id
        where vis.item_sku_id in
        <foreach item="skuId" collection="skuIds" open="(" separator="," close=")">
        #{skuId}
        </foreach>
        and vii.is_deleted = false
    </select>


    <select id="selectItemListByCategory" resultMap="ActivityItemVoMap">
        select
        vii.item_id,
        vii.shop_id,
        vii.item_name,
        vii.platform_first_cid,
        vii.platform_second_cid,
        (select item_categ_name from vanx_platf_item_category where item_categ_id = vii.platform_first_cid)
        as platformFirstCatogName,
        (select item_categ_name from vanx_platf_item_category where item_categ_id = vii.platform_second_cid)
        as platformSecondCatogName,
        vii.item_advert,
        vii.product_code,
        vii.is_sale,
        vis.item_sku_id,
        vis.item_sku_name,
        vis.inventory,
        visp.currency_type,
        visp.sell_price,
        vispc.sku_pic_id,
        vispc.sku_id,
        vispc.pic_name,
        vispc.pic_url as skuPicUrl,
        vai.item_attr_value_id,   <!-- 新增字段，用于映射ItemAttrValue -->
        vai.is_added_picture, <!-- 新增字段 -->
        vai.pic_name, <!-- 新增字段 -->
        vai.pic_url as valuePicUrl, <!-- 新增字段 -->
        vai.attr_name, <!-- 新增字段 -->
        vai.attr_value_str <!-- 新增字段 -->
        from vanx_item_sku vis
        left join vanx_item_info vii on vii.item_id = vis.item_id
        left join vanx_item_sku_price visp on vis.item_sku_id = visp.sku_id
        left join vanx_item_sku_picture vispc on vis.item_sku_id = vispc.sku_id
        left join vanx_item_attr_value vai on vai.item_id = vii.item_id and vai.sku_id = vis.item_sku_id
        where vis.platform_fourth_cid = #{categoryId}
        and vii.is_deleted = false
    </select>

    <!--查询活动商品详情-->
    <select id="selectActivityItemDetail" resultMap="ActivityItemVoMap">
        select
        vii.item_id,
        vii.item_name,
        vii.platform_first_cid,
        vii.platform_second_cid,
        (select item_categ_name from vanx_platf_item_category where item_categ_id = vii.platform_first_cid)
        as platformFirstCatogName,
        (select item_categ_name from vanx_platf_item_category where item_categ_id = vii.platform_second_cid)
        as platformSecondCatogName,
        vii.item_advert,
        vii.product_code,
        vii.is_sale,
        vis.item_sku_id,
        vis.item_sku_name,
        vis.inventory,
        visp.currency_type,
        visp.sell_price,
        vispc.sku_pic_id,
        vispc.sku_id,
        vispc.pic_name,
        vispc.pic_url as skuPicUrl,
        vai.item_attr_value_id,   <!-- 新增字段，用于映射ItemAttrValue -->
        vai.is_added_picture, <!-- 新增字段 -->
        vai.pic_name, <!-- 新增字段 -->
        vai.pic_url as valuePicUrl, <!-- 新增字段 -->
        vai.attr_name, <!-- 新增字段 -->
        vai.attr_value_str <!-- 新增字段 -->
        from vanx_item_sku vis
        left join vanx_item_info vii on vii.item_id = vis.item_id
        left join vanx_item_sku_price visp on vis.item_sku_id = visp.sku_id
        left join vanx_item_sku_picture vispc on vis.item_sku_id = vispc.sku_id
        left join vanx_item_attr_value vai on vai.item_id = vii.item_id and vai.sku_id = vis.item_sku_id
        where vis.item_id = #{itemId}
        and vii.is_deleted = false
    </select>
</mapper>
