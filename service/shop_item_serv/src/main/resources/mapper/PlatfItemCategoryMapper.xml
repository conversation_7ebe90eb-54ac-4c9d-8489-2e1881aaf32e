<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.shop_item.mapper.PlatfItemCategoryMapper">

    <!--查询商品类目api-->
    <select id="selectItemCategVo" resultType="zx.vanx.shop_item.vo.itemCategVo">
        select item_categ_name,item_categ_level from vanx_platf_item_category where is_deleted=0 and item_categ_id=#{itemCategId}
    </select>
</mapper>
