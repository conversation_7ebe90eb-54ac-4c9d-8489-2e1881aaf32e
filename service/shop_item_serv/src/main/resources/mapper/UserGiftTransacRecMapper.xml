<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.shop_item.mapper.UserGiftTransacRecMapper">


    <select id="getGiftIncomeVoPageList" resultType="zx.vanx.shop_item.vo.GiftIncomeVo">
        SELECT
            vugtr.user_gift_id,
            vugtr.sender_id,
            vugtr.item_gift_quantity,
            vugtr.created_time,
            vugtr.virtu_currency_total_value,
            vii.item_name as giftName,
            visc.pic_url
        FROM vanx_user_gift_transac_rec vugtr
                 LEFT JOIN vanx_item_sku_cartoon visc
                           ON vugtr.item_gift_id = visc.item_id
                               AND vugtr.item_gift_sku_id = visc.sku_id
                 LEFT JOIN vanx_item_info vii
                           ON vugtr.item_gift_id = vii.item_id
        WHERE vugtr.receiver_id = #{receiverId} and vugtr.income_type = #{incomeType}
        ORDER BY vugtr.created_time DESC
    </select>

    <select id="queryGiftSenderList" resultType="zx.vanx.shop_item.vo.GiftIncomeVo">

        SELECT
            vugtr.user_gift_id,
            vugtr.sender_id,
            vugtr.receiver_id,
            '支出' as change_type,
            vugtr.send_date,
            vugtr.item_gift_quantity,
            vugtr.created_time,
            vugtr.virtu_currency_total_value,
            vii.item_name as giftName,
            visc.pic_url
        FROM vanx_user_gift_transac_rec vugtr
                 LEFT JOIN vanx_item_sku_cartoon visc
                           ON vugtr.item_gift_id = visc.item_id
                               AND vugtr.item_gift_sku_id = visc.sku_id
                 LEFT JOIN vanx_item_info vii
                           ON vugtr.item_gift_id = vii.item_id
        WHERE vugtr.sender_id = #{giftSendQueryRequest.userId}
          and vugtr.add_source = #{giftSendQueryRequest.addSource}
          and vugtr.send_date &lt;= #{giftSendQueryRequest.lastDateOfMonth}
        ORDER BY vugtr.created_time DESC
    </select>

    <select id="queryMonthVirtualCurrency" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(virtu_currency_total_value), 0) as totalBalance
        FROM vanx_user_gift_transac_rec vugtr

        WHERE vugtr.sender_id = #{userId}
          and vugtr.add_source = #{addSource}
          and vugtr.send_date &gt;= #{firstDayOfMonth}
            and vugtr.send_date &lt;= #{lastDayOfMonth}
        Group by vugtr.add_source
    </select>

    <select id="getSingleWorksGiftIncomeVoPageList" resultType="zx.vanx.shop_item.vo.GiftIncomeVo">

        SELECT
            vugtr.user_gift_id,
            vugtr.sender_id,
            vugtr.item_gift_quantity,
            vugtr.created_time,
            vugtr.virtu_currency_total_value,
            vii.item_name as giftName,
            visc.pic_url
        FROM vanx_user_gift_transac_rec vugtr
                 LEFT JOIN vanx_item_sku_cartoon visc
                           ON vugtr.item_gift_id = visc.item_id
                               AND vugtr.item_gift_sku_id = visc.sku_id
                 LEFT JOIN vanx_item_info vii
                           ON vugtr.item_gift_id = vii.item_id
        WHERE vugtr.receiver_id = #{receiverId} and vugtr.income_type = #{incomeType} and vugtr.gift_object_id = #{giftObjectId}
        ORDER BY vugtr.created_time DESC
    </select>
</mapper>
