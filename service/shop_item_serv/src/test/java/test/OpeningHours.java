package test;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class OpeningHours {

    public static void main(String[] args) throws ParseException {
        String input = "07/09-07/10 周二-周三 08:30-19:30开放;07/12-08/20 08:30-19:30开放;08/21-12/31 08:30-17:30开放;07/11 周四 08:00-19:30开放";
        List<String> result = splitOpeningHours(input);
        for (String s : result) {

            System.out.println(s);
            List<OpenDateAndTime> openDateAndTimes = parseOpenDateAndTime(s);
            System.out.println(openDateAndTimes);
        }
    }

    public static List<String> splitOpeningHours(String input) {
        List<String> resultList = new ArrayList<>();

        // 用分号分割输入字符串
        String[] periods = input.split(";");
        for (String period : periods) {
            resultList.add(period.trim());
        }

        return resultList;
    }


    public static List<OpenDateAndTime> parseOpenDateAndTime(String input) throws ParseException {
        List<OpenDateAndTime> resultList = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("MM/dd/yyyy", Locale.CHINA);

        // 获取当前年份
        int currentYear = Calendar.getInstance().get(Calendar.YEAR);

        // 去掉“开放”两个字
        input = input.replace("开放", "").trim();

        // 将日期和时间部分分开
        String[] parts = input.split(" ");
        if (parts.length >= 2) {
            String datePart = parts[0]; // 例如：11/01-03/31
            String timePart = parts[1]; // 例如：08:30-17:00

            // 如果时间部分被分隔开，重组时间
            if (parts.length > 2) {
                timePart = parts[2];
            }

            // 处理日期范围
            String[] dates = datePart.split("-");
            if (dates.length == 2) {
                // 解析开始和结束日期
                Date startDate = dateFormat.parse(dates[0] + "/" + currentYear);
                Date endDate;

                // 检查是否跨年
                int startMonth = Integer.parseInt(dates[0].split("/")[0]);
                int endMonth = Integer.parseInt(dates[1].split("/")[0]);

                if (startMonth > endMonth) { // 跨年情况，结束日期为下一年
                    endDate = dateFormat.parse(dates[1] + "/" + (currentYear + 1));
                } else {
                    endDate = dateFormat.parse(dates[1] + "/" + currentYear);
                }

                // 使用 Calendar 类迭代日期范围内的每一天
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(startDate);

                while (!calendar.getTime().after(endDate)) {
                    String currentDateString = new SimpleDateFormat("MM/dd", Locale.CHINA).format(calendar.getTime());
                    OpenDateAndTime openDateAndTime = new OpenDateAndTime(currentDateString, timePart);
                    resultList.add(openDateAndTime);
                    calendar.add(Calendar.DATE, 1);
                }
            } else {
                // 处理单一日期
                String currentDateString = new SimpleDateFormat("MM/dd", Locale.CHINA).format(dateFormat.parse(dates[0] + "/" + currentYear));
                OpenDateAndTime openDateAndTime = new OpenDateAndTime(currentDateString, timePart);
                resultList.add(openDateAndTime);
            }
        }

        return resultList;
    }
}
