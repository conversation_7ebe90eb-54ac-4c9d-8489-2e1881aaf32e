service_name=shop-item
service_port=9084
#检查删除容器
if docker ps -a --format '{{.Names}}' | grep -q "^$service_name$"; then
            docker rm -f $service_name
fi

#检查删除镜像
if docker images --format '{{.Repository}}:{{.Tag}}' | grep -q "^$service_name:"; then
            docker rmi $service_name
fi

#创建镜像
docker build -t $service_name .
#创建容器
docker run -e TZ=Asia/Shanghai -d --name $service_name --net=host -p $service_port:$service_port $service_name