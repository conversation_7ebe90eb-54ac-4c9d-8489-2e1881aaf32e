# 用户处罚记录查询接口说明

## 接口概述

新增了一个根据用户ID查询用户处罚记录列表的接口，该接口会返回用户的基本信息（从Redis获取）以及该用户的所有处罚记录详情。

## API接口

### 查询用户处罚记录列表

**接口地址：** `GET /social_circle/social-circle-punishments/user/{userId}`

**接口描述：** 根据用户ID查询某用户的处罚记录列表，包含用户基本信息和处罚记录详情

**请求参数：**
- `userId` (Long, 必填): 用户ID

**响应数据结构：**

```json
{
  "code": 200,
  "message": "查询成功，用户共有1条有效处罚记录",
  "data": {
    "userId": 123456789,
    "userNickName": "用户昵称",
    "userAvatar": "https://example.com/avatar.jpg",
    "hasActivePunishment": true,
    "punishmentsList": [
      {
        "circlePunishmentsId": 1001,
        "socialCircleId": 2001,
        "socialCircleName": "示例圈子",
        "punishmentUserId": 123456789,
        "punishmentType": "禁言",
        "punishmentStartDate": "2025-08-01 10:00:00",
        "punishmentEndDate": "2025-08-02 10:00:00",
        "punishmentDuration": "禁言24小时",
        "punishmentReason": "发布不当内容",
        "punishmentStatus": "有效",
        "userAppealStatus": null,
        "reasonForAppeal": null,
        "checkUserId": 999,
        "createdTime": "2025-08-01 10:00:00"
      }
    ]
  }
}
```

## 实现特性

### 1. 用户信息获取
- 从Redis缓存中获取用户详情信息
- 包含用户昵称和头像URL
- 使用`PermissKey.USER_DETAIL + userId`作为Redis键

### 2. 处罚记录查询
- 查询`vanx_social_circle_punishments`表中状态为"有效"的用户处罚记录
- 按创建时间倒序排列，最新的处罚记录在前
- 返回是否有有效处罚的布尔值标识

### 3. 圈子信息补充
- 自动查询并补充圈子名称信息
- 通过`SocialCircleInfoService`获取圈子详情

### 4. 数据转换
- 使用`BeanUtil.copyProperties`进行实体到VO的转换
- 保持数据结构的一致性和完整性

## 错误处理

- 参数验证：用户ID不能为空
- 异常处理：Redis获取用户信息失败时记录警告日志但不影响主流程
- 圈子信息获取失败时记录警告日志但不影响主流程

## 使用示例

```bash
# 查询用户ID为123456789的处罚记录
GET /social_circle/social-circle-punishments/user/123456789
```

## 相关文件

### 新增文件
- `SocialCirclePunishmentsUserVO.java` - 用户处罚记录视图对象
- `UserPunishmentsListVO.java` - 用户处罚列表视图对象

### 修改文件
- `SocialCirclePunishmentsUserService.java` - 添加查询方法接口
- `SocialCirclePunishmentsUserServiceImpl.java` - 实现查询业务逻辑
- `SocialCirclePunishmentsController.java` - 添加新的API接口
- `SocialCirclePunishmentsUser.java` - 修正表名映射

## 注意事项

1. 该接口只查询状态为"有效"的处罚记录，不包括已解除的处罚
2. 查询的是`vanx_social_circle_punishments`表，与帖子处罚使用同一张表
3. 用户信息从Redis获取，确保数据的实时性
4. `hasActivePunishment`字段表示用户是否有有效的处罚记录
5. 返回的`punishmentsList`只包含有效的处罚记录
