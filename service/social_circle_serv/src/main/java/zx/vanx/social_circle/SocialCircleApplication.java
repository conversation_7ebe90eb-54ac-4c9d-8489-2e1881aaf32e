package zx.vanx.social_circle;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"zx.vanx.user_permiss.client"})
@EnableScheduling
@MapperScan({ "zx.vanx.social_circle.mapper" })
@ComponentScan(basePackages = "zx.vanx")
public class SocialCircleApplication {

    public static void main(String[] args) {
        SpringApplication.run(SocialCircleApplication.class, args);
    }
}
