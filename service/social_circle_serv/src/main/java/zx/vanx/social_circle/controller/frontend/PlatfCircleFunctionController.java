package zx.vanx.social_circle.controller.frontend;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.social_circle.service.PlatfCircleFunctionService;
import zx.vanx.social_circle.vo.CircleFunctionMenuVO;

import java.util.List;

/**
 * <p>
 * 菜单功能表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@RestController
@RequestMapping("/social_circle/platf-circle-function")
@Api(tags = "社交圈子菜单")
@RequiredArgsConstructor
public class PlatfCircleFunctionController {

    private final PlatfCircleFunctionService platfCircleFunctionService;

    @ApiOperation("查询指定功能菜单下的子菜单树")
    @GetMapping("/submenu-tree")
    public ResultData<List<CircleFunctionMenuVO>> getSubMenuTree(
            @RequestParam(value = "functionTitle", defaultValue = "圈子详情")
            @ApiParam(value = "父级功能菜单标题", example = "圈子详情")
            String functionTitle) {

        List<CircleFunctionMenuVO> menuTree = platfCircleFunctionService.getSubMenuTree(functionTitle);

        return ResultData.ok(menuTree).Message(functionTitle + "下的子菜单功能树查询成功");
    }

    @ApiOperation("查询圈主菜单树")
    @GetMapping("/owner-menu-tree")
    public ResultData<List<CircleFunctionMenuVO>> getOwnerMenuTree(
            @RequestParam
            @ApiParam(value = "圈子ID", required = true, example = "1001")
            Long socialCircleId) {

        List<CircleFunctionMenuVO> menuTree = platfCircleFunctionService.getOwnerMenuTree(socialCircleId);

        return ResultData.ok(menuTree).Message("圈主菜单树查询成功");
    }
}