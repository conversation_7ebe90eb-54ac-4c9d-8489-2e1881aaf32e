package zx.vanx.social_circle.controller.frontend;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.social_circle.service.PlatfPostCategService;
import zx.vanx.social_circle.vo.PlatfPostCategVO;

import java.util.List;

/**
 * <p>
 * 平台帖子分类管理 前端控制器
 * 提供分类树形结构的查询等RESTful API接口
 * 支持多级分类的递归展示
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Api(tags = "社交圈子帖子分类")
@RestController
@RequiredArgsConstructor
@RequestMapping("/social_circle/platf-post-categ")
public class PlatfPostCategController {

    private final PlatfPostCategService platfPostCategService;

    @ApiOperation("递归查询分类树形结构")
    @GetMapping("/tree")
    public ResultData<List<PlatfPostCategVO>> getCategoryTree(
            @RequestParam(required = false) 
            @ApiParam(value = "圈子ID", example = "1") 
            Long socialCircleId) {

        // 查询分类树形结构（通过圈子ID查询）
        List<PlatfPostCategVO> categoryTree = platfPostCategService.getCategoryTreeByCircleId(socialCircleId);

        // 返回结果
        if (categoryTree == null || categoryTree.isEmpty()) {
            return ResultData.info("暂无分类数据", categoryTree);
        }

        return ResultData.ok(categoryTree).Message("分类树查询成功");
    }

}

