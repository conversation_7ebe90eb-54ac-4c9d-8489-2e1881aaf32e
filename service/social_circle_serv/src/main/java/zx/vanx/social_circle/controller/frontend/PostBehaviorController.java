package zx.vanx.social_circle.controller.frontend;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.social_circle.request.PostBehaviorRequest;
import zx.vanx.social_circle.service.PostBehaviorService;
import zx.vanx.social_circle.vo.PostBehaviorStatusVo;

/**
 * <p>
 * 圈子帖子用户行为控制器（点赞和收藏）
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Api(tags = "社交圈子帖子用户行为")
@RestController
@RequiredArgsConstructor
@RequestMapping("/social_circle/post-behavior")
public class PostBehaviorController {

    private final PostBehaviorService postBehaviorService;

    @ApiOperation("点赞帖子")
    @PostMapping("/like")
    public ResultData<String> likePost(@RequestBody PostBehaviorRequest request) {
        Long userId = LoginHelper.getUserId();
        postBehaviorService.likePost(request.getPostId(), userId);
        return ResultData.ok("点赞成功");
    }

    @ApiOperation("取消点赞")
    @PostMapping("/unlike")
    public ResultData<String> unlikePost(@RequestBody PostBehaviorRequest request) {
        Long userId = LoginHelper.getUserId();
        postBehaviorService.unlikePost(request.getPostId(), userId);
        return ResultData.ok("取消点赞成功");
    }

    @ApiOperation("收藏帖子")
    @PostMapping("/collect")
    public ResultData<String> collectPost(@RequestBody PostBehaviorRequest request) {
        Long userId = LoginHelper.getUserId();
        postBehaviorService.collectPost(request.getPostId(), userId);
        return ResultData.ok("收藏成功");
    }

    @ApiOperation("取消收藏")
    @PostMapping("/uncollect")
    public ResultData<String> uncollectPost(@RequestBody PostBehaviorRequest request) {
        Long userId = LoginHelper.getUserId();
        postBehaviorService.uncollectPost(request.getPostId(), userId);
        return ResultData.ok("取消收藏成功");
    }

    @ApiOperation("获取帖子行为状态")
    @GetMapping("/status/{postId}")
    public ResultData<PostBehaviorStatusVo> getPostBehaviorStatus(@PathVariable Long postId) {
        Long userId = LoginHelper.getUserId();
        PostBehaviorStatusVo status = postBehaviorService.getPostBehaviorStatus(postId, userId);
        return ResultData.ok(status);
    }

    @ApiOperation("获取帖子点赞数")
    @GetMapping("/likes/count/{postId}")
    public ResultData<Long> getPostLikesCount(@PathVariable Long postId) {
        Long count = postBehaviorService.getPostLikesCount(postId);
        return ResultData.ok(count);
    }

    @ApiOperation("获取帖子收藏数")
    @GetMapping("/collects/count/{postId}")
    public ResultData<Long> getPostCollectsCount(@PathVariable Long postId) {
        Long count = postBehaviorService.getPostCollectsCount(postId);
        return ResultData.ok(count);
    }
}