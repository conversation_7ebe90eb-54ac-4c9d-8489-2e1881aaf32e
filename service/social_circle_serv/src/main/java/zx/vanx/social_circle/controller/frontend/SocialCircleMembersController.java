package zx.vanx.social_circle.controller.frontend;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.social_circle.entity.SocialCircleMembers;
import zx.vanx.social_circle.request.SocialCircleJoinRequest;
import zx.vanx.social_circle.request.SocialCircleLeaveRequest;
import zx.vanx.social_circle.request.SocialCircleMemberBatchApprovalRequest;
import zx.vanx.social_circle.request.SocialCircleMemberRoleChangeRequest;
import zx.vanx.social_circle.request.SocialCircleMembersPageRequest;
import zx.vanx.social_circle.service.SocialCircleMembersService;
import zx.vanx.social_circle.vo.SocialCircleMembersVO;
import zx.vanx.common_util.core.page.PageObj;

import javax.validation.Valid;

import java.util.List;

/**
 * <p>
 * 圈子成员表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Api(tags = "社交圈子人员")
@RestController
@RequiredArgsConstructor
@RequestMapping("/social_circle/social-circle-members")
public class SocialCircleMembersController {

    private final SocialCircleMembersService socialCircleMembersService;

    @ApiOperation("分页查询圈子成员列表")
    @PostMapping("/page")
    public ResultData<PageObj<SocialCircleMembersVO>> getSocialCircleMembersPage(@Valid @RequestBody SocialCircleMembersPageRequest request) {

        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();

        // 调用Service层分页查询圈子成员列表
        PageObj<SocialCircleMembersVO> memberPage = socialCircleMembersService.getSocialCircleMembersPage(request, userId);

        // 返回分页结果
        return ResultData.ok(memberPage).Message("圈子成员分页查询成功");
    }

    @ApiOperation("申请加入圈子")
    @PostMapping("/apply-join")
    public ResultData<SocialCircleMembers> applyJoinCircle(@Valid @RequestBody SocialCircleJoinRequest request) {
        
        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();
        
        // 调用Service层处理申请加入圈子
        SocialCircleMembers appliedMember = socialCircleMembersService.applyJoinCircle(
            request.getSocialCircleCode(), 
            request.getReasonForAdd(), 
            userId
        );
        
        // 返回申请结果
        return ResultData.ok(appliedMember).Message("圈子申请提交成功，请等待审核");
    }

    @ApiOperation("批量审批圈子申请")
    @PostMapping("/batch-approval")
    public ResultData<Integer> batchApprovalMembers(@Valid @RequestBody SocialCircleMemberBatchApprovalRequest request) {
        
        // 获取当前登录用户ID（操作者）
        Long operatorUserId = LoginHelper.getUserId();
        
        // 调用Service层处理批量审批
        Integer processedCount = socialCircleMembersService.batchApprovalMembers(
            request.getMemberIds(),
            request.getApprovalAction(),
            request.getRejectReason(),
            operatorUserId
        );
        
        // 返回审批结果
        String action = "同意".equals(request.getApprovalAction()) ? "通过" : "驳回";
        return ResultData.ok(processedCount).Message("批量审批完成，共" + action + processedCount + "个申请");
    }

    @ApiOperation("退出圈子")
    @PostMapping("/leave")
    public ResultData<SocialCircleMembers> leaveCircle(@Valid @RequestBody SocialCircleLeaveRequest request) {
        
        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();
        
        // 调用Service层处理退出圈子
        SocialCircleMembers leftMember = socialCircleMembersService.leaveCircle(
            request.getSocialCircleId(),
            request.getLeaveReason(),
            userId
        );
        
        // 返回退出结果
        return ResultData.ok(leftMember).Message("成功退出圈子");
    }

    @ApiOperation("圈主修改成员角色")
    @PostMapping("/change-role")
    public ResultData<SocialCircleMembers> changeMemberRole(@Valid @RequestBody SocialCircleMemberRoleChangeRequest request) {
        
        // 获取当前登录用户ID（发起修改的圈主ID）
        Long ownerUserId = LoginHelper.getUserId();
        
        // 调用Service层处理角色修改
        SocialCircleMembers updatedMember = socialCircleMembersService.changeMemberRole(
            request.getSocialCircleId(),
            request.getMemberId(),
            request.getNewRole(),
            request.getChangeReason(),
            ownerUserId
        );
        
        // 返回修改结果
        return ResultData.ok(updatedMember).Message("成员角色修改成功");
    }

    @ApiOperation("查询圈子管理员列表")
    @GetMapping("/managers")
    public ResultData<List<SocialCircleMembersVO>> getCircleManagers(@RequestParam Long circleId) {

        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();

        // 调用Service层查询圈子管理员列表
        List<SocialCircleMembersVO> circleManagers = socialCircleMembersService.getCircleManagers(userId, circleId);

        // 返回管理员列表信息
        return ResultData.ok(circleManagers).Message("圈子管理员列表查询成功");

    }

}

