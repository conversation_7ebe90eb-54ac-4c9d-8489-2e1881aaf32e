package zx.vanx.social_circle.controller.frontend;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.social_circle.entity.SocialCirclePlatfCategory;
import zx.vanx.social_circle.service.SocialCircleCategoryRelatService;

import java.util.List;

/**
 * <p>
 * vanx_platf_item_category 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Api(tags = "社交圈子分类")
@RestController
@RequiredArgsConstructor
@RequestMapping("/social_circle/social-circle-platf-category")
public class SocialCirclePlatfCategoryController {

    private final SocialCircleCategoryRelatService socialCircleCategoryRelatService;

    @ApiOperation("查询平台圈子分类树")
    @PostMapping("/tree")
    public ResultData<List<SocialCirclePlatfCategory>> getCategTree() {

        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();

        // 调用Service层创建圈子
        List<SocialCirclePlatfCategory> socialCirclePlatfCategory = socialCircleCategoryRelatService.getCategTree();

        // 返回创建成功的圈子信息
        return ResultData.ok(socialCirclePlatfCategory).Message("平台圈子分类树查询成功");
    }


}

