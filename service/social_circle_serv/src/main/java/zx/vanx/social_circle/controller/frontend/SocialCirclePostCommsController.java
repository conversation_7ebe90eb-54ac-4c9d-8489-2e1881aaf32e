package zx.vanx.social_circle.controller.frontend;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.social_circle.request.SysObjectCommsAddRequest;
import zx.vanx.social_circle.service.SocialCirclePostCommsService;
import zx.vanx.social_circle.vo.SysObjectCommsVo;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 用户系统对象的评论表 前端控制器
 * 提供帖子评论的增删查改功能
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Api(tags = "社交圈子帖子评论")
@RestController
@RequiredArgsConstructor
@RequestMapping("/social_circle/social-circle-post-comms")
public class SocialCirclePostCommsController {

    private final SocialCirclePostCommsService socialCirclePostCommsService;

    @ApiOperation("添加评论")
    @PostMapping("/add")
    public ResultData<SysObjectCommsVo> addSysObjectComms(@Valid @RequestBody SysObjectCommsAddRequest request) {
        SysObjectCommsVo result = socialCirclePostCommsService.addSysObjectComms(request);
        return ResultData.ok(result).Message("评论添加成功");
    }

    @ApiOperation("删除评论")
    @DeleteMapping("/delete")
    public ResultData<Object> deleteSysObjectComms(@RequestParam Long objectCommsId,
                                                   @RequestParam Long circlePostId) {
        Long userId = LoginHelper.getUserId();
        socialCirclePostCommsService.deleteSysObjectComms(objectCommsId, circlePostId, userId);
        return ResultData.ok().Message("评论删除成功");
    }

    @ApiOperation("查询帖子一级评论")
    @GetMapping("/list")
    public ResultData<List<SysObjectCommsVo>> selectSysObjectComms(@RequestParam Long circlePostId,
                                                                   @RequestParam Long currentPage,
                                                                   @RequestParam Long limit) {
        List<SysObjectCommsVo> result = socialCirclePostCommsService.selectSysObjectComms(circlePostId,currentPage,limit);
        return ResultData.ok(result).Message("一级评论查询成功");
    }

    @ApiOperation("查询帖子回复评论")
    @GetMapping("/sub-list")
    public ResultData<List<SysObjectCommsVo>> selectSubSysObjectComms(@RequestParam Long topCommsId,
                                                                      @RequestParam Long currentPage,
                                                                      @RequestParam Long limit) {
        List<SysObjectCommsVo> result = socialCirclePostCommsService.selectSubSysObjectComms(topCommsId,currentPage,limit);
        return ResultData.ok(result).Message("子评论查询成功");
    }
}
