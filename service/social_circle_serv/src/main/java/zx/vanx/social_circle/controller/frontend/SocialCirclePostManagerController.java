package zx.vanx.social_circle.controller.frontend;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.core.page.PageObj;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.social_circle.service.SocialCirclePostService;
import zx.vanx.social_circle.vo.PostManagementVO;
import zx.vanx.social_circle.request.PostStatusUpdateRequest;

import javax.validation.Valid;

/**
 * <p>
 * 社交圈子帖子管理控制器
 * 提供圈主管理帖子的相关功能
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Api(tags = "社交圈子帖子管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/social_circle/post-manager")
public class SocialCirclePostManagerController {

    private final SocialCirclePostService socialCirclePostService;

    @ApiOperation("分页查询圈子帖子列表（圈主专用）")
    @GetMapping("/posts")
    public ResultData<PageObj<PostManagementVO>> getCirclePostsForManagement(
            @RequestParam Long circleId,
            @RequestParam(defaultValue = "1") Long page,
            @RequestParam(defaultValue = "10") Long limit) {

        // 获取当前登录用户ID
        Long currentUserId = LoginHelper.getUserId();

        // 调用Service层查询圈子帖子管理列表
        PageObj<PostManagementVO> postPage = socialCirclePostService.getCirclePostsForManagement(
                circleId, page, limit, currentUserId);

        // 返回分页结果
        return ResultData.ok(postPage).Message("圈子帖子列表查询成功");
    }

    @ApiOperation("修改帖子状态（圈主专用）")
    @PutMapping("/posts/status")
    public ResultData<PostManagementVO> updatePostStatus(@Valid @RequestBody PostStatusUpdateRequest request) {

        // 获取当前登录用户ID
        Long currentUserId = LoginHelper.getUserId();

        // 调用Service层修改帖子状态
        PostManagementVO updatedPost = socialCirclePostService.updatePostStatus(request, currentUserId);

        // 返回修改结果
        return ResultData.ok(updatedPost).Message("帖子状态修改成功");
    }
}
