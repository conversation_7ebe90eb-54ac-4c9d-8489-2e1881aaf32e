package zx.vanx.social_circle.controller.frontend;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.core.page.PageObj;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.social_circle.request.PostReportPageRequest;
import zx.vanx.social_circle.request.PostReportRequest;
import zx.vanx.social_circle.service.SocialCirclePostReportService;
import zx.vanx.social_circle.vo.PostReportManagementVO;

import javax.validation.Valid;

/**
 * <p>
 * 帖子举报信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Api(tags = "社交圈子帖子举报")
@RestController
@RequiredArgsConstructor
@RequestMapping("/social_circle/social-circle-post-report")
public class SocialCirclePostReportController {

    private final SocialCirclePostReportService socialCirclePostReportService;

    @ApiOperation("举报帖子")
    @PostMapping("/report")
    public ResultData<String> reportPost(@Valid @RequestBody PostReportRequest request) {
        
        // 获取当前登录用户ID
        Long reportUserId = LoginHelper.getUserId();
        
        // 调用服务层处理举报（内部已包含防重复举报逻辑）
        socialCirclePostReportService.reportPost(request, reportUserId);
        
        return ResultData.ok("举报提交成功，我们会尽快处理");
    }

    @ApiOperation("管理员查询举报清单")
    @PostMapping("/management/list")
    public ResultData<PageObj<PostReportManagementVO>> getReportManagementList(@Valid @RequestBody PostReportPageRequest request) {

        // 获取当前登录用户ID
        Long currentUserId = LoginHelper.getUserId();

        // 调用服务层获取举报清单（包含圈主权限验证）
        PageObj<PostReportManagementVO> pageResult = socialCirclePostReportService.getReportManagementList(request, currentUserId);

        // 构建响应消息
        String message = String.format("查询成功，共找到%d条举报记录", pageResult.getTotal());

        return ResultData.ok(pageResult).Message(message);
    }

}
