package zx.vanx.social_circle.controller.frontend;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.social_circle.request.SocialCirclePunishmentsAddRequest;
import zx.vanx.social_circle.service.SocialCirclePunishmentsPostService;
import zx.vanx.social_circle.vo.SocialCirclePunishmentsPostVO;

/**
 * <p>
 * 圈子处罚记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Api(tags = "社交圈子处罚")
@RestController
@RequiredArgsConstructor
@RequestMapping("/social_circle/social-circle-punishments")
public class SocialCirclePunishmentsController {

    private final SocialCirclePunishmentsPostService socialCirclePunishmentsPostService;

    // 新增用户处罚
    @ApiOperation("处罚圈子成员")
    @PostMapping("/add")
    public ResultData<SocialCirclePunishmentsPostVO> addUserPunishments(@RequestBody SocialCirclePunishmentsAddRequest publishRequest) {

        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();

        // 调用Service层创建圈子
        SocialCirclePunishmentsPostVO socialCirclePunishmentsPostVO = socialCirclePunishmentsPostService.addUserPunishments(userId,publishRequest);

        // 返回创建成功的圈子信息
        return ResultData.ok(socialCirclePunishmentsPostVO).Message("处罚成功");
    }

}

