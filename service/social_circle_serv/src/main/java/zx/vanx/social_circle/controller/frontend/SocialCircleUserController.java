package zx.vanx.social_circle.controller.frontend;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.social_circle.service.SocialCircleUserService;
import zx.vanx.social_circle.vo.CircleUserDetailVo;

/**
 * <p>
 * 社交圈子用户 前端控制器
 * 提供圈子用户相关的接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Api(tags = {"社交圈子用户接口"})
@RestController
@RequestMapping("/social_circle/social-circle-user")
@RequiredArgsConstructor
@Validated
@Slf4j
public class SocialCircleUserController {

    private final SocialCircleUserService socialCircleUserService;

    @ApiOperation("获取当前登录用户的圈子详情")
    @PostMapping("/getCircleUserDetail")
    public ResultData<CircleUserDetailVo> getCircleUserDetail() {

        try {
            // 获取当前登录用户ID
            Long userId = LoginHelper.getUserId();

            // 调用服务获取圈子用户详情
            CircleUserDetailVo circleUserDetail = socialCircleUserService.getCircleUserDetail(userId);
            
            if (circleUserDetail == null) {
                return ResultData.error("未找到用户信息");
            }
            
            return ResultData.ok(circleUserDetail);
            
        } catch (Exception e) {
            return ResultData.error("获取用户信息失败");
        }
    }

    @ApiOperation("根据用户ID获取圈子用户详情")
    @PostMapping("/getCircleUserDetail/{userId}")
    public ResultData<CircleUserDetailVo> getCircleUserDetailByUserId(@PathVariable Long userId) {
        try {

            if (userId == null) {
                return ResultData.error("用户ID不能为空");
            }
            
            // 调用服务获取圈子用户详情
            CircleUserDetailVo circleUserDetail = socialCircleUserService.getCircleUserDetail(userId);
            
            if (circleUserDetail == null) {
                return ResultData.error("未找到用户信息");
            }
            
            return ResultData.ok(circleUserDetail);
            
        } catch (Exception e) {
            return ResultData.error("获取用户信息失败");
        }
    }

}
