package zx.vanx.social_circle.controller.internal;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;
import zx.vanx.social_circle.dto.UserCircleStatsDto;
import zx.vanx.social_circle.entity.SocialCircleInfo;
import zx.vanx.social_circle.entity.SocialCircleMembers;
import zx.vanx.social_circle.service.SocialCircleInfoService;
import zx.vanx.social_circle.service.SocialCircleMembersService;
import zx.vanx.social_circle.service.SocialCirclePostService;

/**
 * 社交圈子统计信息内部接口Controller
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/social_circle/stats")
@Api(tags = "社交圈子统计信息内部接口")
@ApiIgnore
public class SocialCircleStatsInternalController {

    private final SocialCircleMembersService socialCircleMembersService;
    private final SocialCirclePostService socialCirclePostService;
    private final SocialCircleInfoService socialCircleInfoService;

    /**
     * 查询用户默认圈子统计信息
     */
    @ApiOperation("查询用户默认圈子统计信息")
    @GetMapping("/user/{userId}")
    public UserCircleStatsDto getUserCircleStats(@PathVariable("userId") Long userId) {
        try {
            if (userId == null) {
                log.warn("用户ID不能为空");
                return createDefaultStats(userId);
            }
            
            UserCircleStatsDto statsDto = new UserCircleStatsDto();
            statsDto.setUserId(userId);
            
            // 1. 查询用户的默认圈子信息
            LambdaQueryWrapper<SocialCircleMembers> defaultCircleQuery = new LambdaQueryWrapper<>();
            defaultCircleQuery.eq(SocialCircleMembers::getUserId, userId)
                    .eq(SocialCircleMembers::getIsDefault, true)
                    .eq(SocialCircleMembers::getUserStatus, "已入圈");
            
            SocialCircleMembers defaultMember = socialCircleMembersService.getOne(defaultCircleQuery);
            if (defaultMember == null) {
                // 用户没有默认圈子，返回空统计信息
                return createDefaultStats(userId);
            }
            
            // 2. 获取默认圈子详细信息
            SocialCircleInfo circleInfo = socialCircleInfoService.getById(defaultMember.getSocialCircleId());
            if (circleInfo != null) {
                statsDto.setDefaultCircleId(circleInfo.getSocialCircleId());
                statsDto.setDefaultCircleName(circleInfo.getSocialCircleName());
            }
            
            // 3. 查询用户默认圈子成员数
            Long memberCount = socialCircleMembersService.getDefaultCircleMemberCount(userId);
            statsDto.setMemberCount(memberCount != null ? memberCount : 0L);
            
            // 4. 查询用户发布的帖子总数
            Long postCount = socialCirclePostService.getUserPostCount(userId);
            statsDto.setPostCount(postCount != null ? postCount : 0L);
            
            // 5. 设置获赞数（暂时设为0，后续补充）
            statsDto.setLikesCount(0L);
            
            return statsDto;
        } catch (Exception e) {
            log.error("查询用户默认圈子统计信息失败，用户ID: {}, 错误信息: {}", userId, e.getMessage(), e);
            return createDefaultStats(userId);
        }
    }
    
    /**
     * 创建默认统计信息
     */
    private UserCircleStatsDto createDefaultStats(Long userId) {
        UserCircleStatsDto defaultStats = new UserCircleStatsDto();
        defaultStats.setUserId(userId);
        defaultStats.setDefaultCircleId(null);
        defaultStats.setDefaultCircleName("");
        defaultStats.setPostCount(0L);
        defaultStats.setMemberCount(0L);
        defaultStats.setLikesCount(0L);
        return defaultStats;
    }
}