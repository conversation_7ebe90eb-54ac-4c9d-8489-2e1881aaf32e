package zx.vanx.social_circle.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import zx.vanx.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 圈子处罚记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("vanx_social_circle_punishments_user")
@ApiModel(value="SocialCirclePunishmentsUser对象", description="圈子处罚记录表")
public class SocialCirclePunishmentsUser extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "circle_punishments_id", type = IdType.NONE)
    private Long circlePunishmentsId;

    @ApiModelProperty(value = "圈子id")
    private Long socialCircleId;

    @ApiModelProperty(value = "被处罚的用户id")
    private Long punishmentUserId;

    @ApiModelProperty(value = "处罚的类型：警告、禁言、封号、限制权限等")
    private String punishmentType;

    @ApiModelProperty(value = "处罚的开始时间")
    private Date punishmentStartDate;

    @ApiModelProperty(value = "处罚的结束时间")
    private Date punishmentEndDate;

    @ApiModelProperty(value = "处罚持续时间：禁言24小时")
    private String punishmentDuration;

    @ApiModelProperty(value = "处罚的原因")
    private String punishmentReason;

    @ApiModelProperty(value = "处罚状态：有效、已解除")
    private String punishmentStatus;

    @ApiModelProperty(value = "用户处罚申诉的状态：申诉处理中、申诉通过、申诉被驳回")
    private String userAppealStatus;

    @ApiModelProperty(value = "申诉的理由")
    private String reasonForAppeal;

    @ApiModelProperty(value = "进行处罚的管理员或用户id")
    private Long checkUserId;


}
