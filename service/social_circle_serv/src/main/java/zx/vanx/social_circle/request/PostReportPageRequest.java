package zx.vanx.social_circle.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 管理员举报清单分页查询请求
 * 用于管理员查看和筛选被举报的帖子信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@ApiModel(value = "管理员举报清单分页查询请求", description = "管理员举报清单分页查询请求")
public class PostReportPageRequest {

    @ApiModelProperty(value = "当前页码", required = true, example = "1", notes = "页码从1开始")
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码必须大于0")
    private Long page;

    @ApiModelProperty(value = "每页记录数", required = true, example = "10", notes = "建议每页10-50条记录")
    @NotNull(message = "每页记录数不能为空")
    @Min(value = 1, message = "每页记录数必须大于0")
    private Long limit;

    @ApiModelProperty(value = "举报处理状态筛选", example = "待处理", notes = "可选参数，筛选包含特定处理状态举报的帖子（待处理/已处理）")
    private String reportProcessingStatus;

    @ApiModelProperty(value = "帖子状态筛选", example = "1", notes = "可选参数，筛选特定状态的帖子")
    private String postStatus;

    @ApiModelProperty(value = "圈子ID", required = true, example = "123456789", notes = "必传参数，管理员只能查看自己管理的圈子举报")
    @NotNull(message = "圈子ID不能为空")
    private Long socialCircleId;

    @ApiModelProperty(value = "排序字段", example = "latest_report_time", notes = "可选参数，支持：latest_report_time(最新举报时间)、report_count(举报数量)、post_created_time(帖子创建时间)")
    private String orderBy;

    @ApiModelProperty(value = "排序方向", example = "DESC", notes = "可选参数，ASC升序或DESC降序，默认DESC")
    private String orderDirection;

}