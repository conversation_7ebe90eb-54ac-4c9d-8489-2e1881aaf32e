package zx.vanx.social_circle.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <p>
 * 社交圈子处罚请求DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Data
public class SocialCirclePunishmentsAddRequest {


    @ApiModelProperty(value = "圈子id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long socialCircleId;

    @ApiModelProperty(value = "被处罚的用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long punishmentUserId;

    @ApiModelProperty(value = "处罚的类型：警告、禁言、封号、限制权限等")
    private String punishmentType;

    @ApiModelProperty(value = "处罚的开始时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date punishmentStartDate;

    @ApiModelProperty(value = "处罚的结束时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date punishmentEndDate;

    @ApiModelProperty(value = "处罚持续时间：禁言24小时")
    private String punishmentDuration;

}
