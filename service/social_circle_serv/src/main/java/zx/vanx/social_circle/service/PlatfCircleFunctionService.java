package zx.vanx.social_circle.service;

import zx.vanx.social_circle.entity.PlatfCircleFunction;
import zx.vanx.social_circle.vo.CircleFunctionMenuVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 菜单功能表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
public interface PlatfCircleFunctionService extends IService<PlatfCircleFunction> {

    /**
     * 查询指定功能菜单下的子菜单树
     * @param functionTitle 父级功能菜单标题
     * @return 子菜单功能树
     */
    List<CircleFunctionMenuVO> getSubMenuTree(String functionTitle);

    /**
     * 根据圈子ID查询圈主菜单树
     * @param socialCircleId 圈子ID
     * @return 圈主菜单树
     */
    List<CircleFunctionMenuVO> getOwnerMenuTree(Long socialCircleId);
}
