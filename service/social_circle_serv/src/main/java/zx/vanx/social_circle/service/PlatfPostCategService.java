package zx.vanx.social_circle.service;

import zx.vanx.social_circle.entity.PlatfPostCateg;
import zx.vanx.social_circle.vo.PlatfPostCategVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * vanx_media_category 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
public interface PlatfPostCategService extends IService<PlatfPostCateg> {

    /**
     * 递归查询分类树形结构
     * 构建完整的多级分类树，支持顶级分类及其所有子分类的层级展示
     * 
     * @param userId 用户ID（可选，如果为null则查询所有用户的分类）
     * @param categType 分类类型（可选，如果为null则查询所有类型）
     * @return 树形结构的分类列表，顶级分类包含其所有子分类
     */
    List<PlatfPostCategVO> getCategoryTree(Long userId, String categType);

    /**
     * 根据圈子ID查询分类树形结构
     * 通过圈子ID在关联表中查找对应的分类，然后构建树形结构
     * 
     * @param socialCircleId 圈子ID（可选，如果为null则查询所有分类）
     * @return 指定圈子的分类树形结构
     */
    List<PlatfPostCategVO> getCategoryTreeByCircleId(Long socialCircleId);

}
