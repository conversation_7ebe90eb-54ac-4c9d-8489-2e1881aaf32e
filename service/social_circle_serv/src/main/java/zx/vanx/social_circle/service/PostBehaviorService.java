package zx.vanx.social_circle.service;

import zx.vanx.social_circle.vo.PostBehaviorStatusVo;

/**
 * <p>
 * 帖子行为服务接口（点赞和收藏）
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
public interface PostBehaviorService {

    /**
     * 点赞帖子
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     */
    void likePost(Long postId, Long userId);

    /**
     * 取消点赞
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     */
    void unlikePost(Long postId, Long userId);

    /**
     * 收藏帖子
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     */
    void collectPost(Long postId, Long userId);

    /**
     * 取消收藏
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     */
    void uncollectPost(Long postId, Long userId);

    /**
     * 获取帖子行为状态
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 行为状态
     */
    PostBehaviorStatusVo getPostBehaviorStatus(Long postId, Long userId);

    /**
     * 获取帖子点赞数
     *
     * @param postId 帖子ID
     * @return 点赞数
     */
    Long getPostLikesCount(Long postId);

    /**
     * 获取帖子收藏数
     *
     * @param postId 帖子ID
     * @return 收藏数
     */
    Long getPostCollectsCount(Long postId);
}