package zx.vanx.social_circle.service;

import zx.vanx.social_circle.entity.SocialCircleCategoryRelat;
import com.baomidou.mybatisplus.extension.service.IService;
import zx.vanx.social_circle.entity.SocialCirclePlatfCategory;

import java.util.List;

/**
 * <p>
 * vanx_media_categ_relat 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
public interface SocialCircleCategoryRelatService extends IService<SocialCircleCategoryRelat> {

    /**
     * 获取平台圈子分类树
     *
     * @return SocialCirclePlatfCategory
     */
    List<SocialCirclePlatfCategory> getCategTree();
}
