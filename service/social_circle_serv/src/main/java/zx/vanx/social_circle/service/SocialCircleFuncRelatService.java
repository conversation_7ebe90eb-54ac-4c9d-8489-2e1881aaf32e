package zx.vanx.social_circle.service;

import zx.vanx.social_circle.entity.SocialCircleFuncRelat;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 圈子菜单权限功能表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
public interface SocialCircleFuncRelatService extends IService<SocialCircleFuncRelat> {

    /**
     * 为圈主创建菜单功能权限关联
     * @param socialCircleId 圈子ID
     * @param selectedFunctionIds 选中的圈子详情菜单ID列表
     */
    void createOwnerFunctionRelations(Long socialCircleId,  List<Long> selectedFunctionIds);

    /**
     * 为普通成员创建菜单功能权限关联
     * @param socialCircleId 圈子ID
     * @param selectedFunctionIds 选中的圈子详情菜单ID列表
     */
    void createMemberFunctionRelations(Long socialCircleId, List<Long> selectedFunctionIds);

}
