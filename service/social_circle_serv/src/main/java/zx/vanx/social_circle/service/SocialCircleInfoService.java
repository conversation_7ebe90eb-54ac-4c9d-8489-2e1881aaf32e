package zx.vanx.social_circle.service;

import com.baomidou.mybatisplus.extension.service.IService;
import zx.vanx.common_util.core.page.PageObj;
import zx.vanx.social_circle.entity.SocialCircleInfo;
import zx.vanx.social_circle.request.SocialCircleCreateRequest;
import zx.vanx.social_circle.request.SocialCircleSearchRequest;
import zx.vanx.social_circle.vo.CircleFunctionMenuVO;
import zx.vanx.social_circle.vo.DefaultCircleVO;
import zx.vanx.social_circle.vo.SocialCircleDetailVO;
import zx.vanx.social_circle.vo.SocialCircleInfoVO;
import zx.vanx.social_circle.vo.SocialCircleSearchVO;
import zx.vanx.social_circle.vo.SocialCircleCreateResultVO;

import java.util.List;

/**
 * <p>
 * 圈子信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface SocialCircleInfoService extends IService<SocialCircleInfo> {

    /**
     * 根据用户ID查询该用户创建的圈子信息
     *
     * @param userId 用户ID
     * @return 圈子信息，如果没有则返回null
     */
    SocialCircleInfo getCircleByUserId(Long userId);

    /**
     * 根据用户ID查询该用户创建的圈子完整详情信息
     * 包含圈子基本信息、地址信息、媒体信息、成员信息等全部相关数据
     *
     * @param userId 用户ID
     * @return 圈子详情信息，如果没有则返回null
     */
    SocialCircleDetailVO getCurrentUserCircleDetail(Long userId);

    /**
     * 根据圈子ID查询圈子完整详情信息
     * 需要验证当前用户是否有权限查看该圈子
     * 包含圈子基本信息、地址信息、媒体信息、成员信息等全部相关数据
     *
     * @param circleId 圈子ID
     * @param userId   当前用户ID
     * @return 圈子详情信息，如果圈子不存在或用户无权限则返回null
     */
    SocialCircleDetailVO getCircleDetailById(Long circleId, Long userId);

    /**
     * 查询用户的默认圈子信息
     * 通过用户在圈子成员表中设置的默认圈子标识查询
     * 返回圈子基本信息、成员数、活跃数等统计数据
     *
     * @param userId 用户ID
     * @return 默认圈子信息，如果用户没有设置默认圈子则返回null
     */
    DefaultCircleVO getMyDefaultCircle(Long userId);

    /**
     * 创建新的社交圈子
     * 包含个人实名认证检查
     *
     * @param request 圈子创建请求参数
     * @param userId  创建者用户ID
     * @return 圈子创建结果（包含认证状态和创建信息）
     */
    SocialCircleCreateResultVO createSocialCircle(SocialCircleCreateRequest request, Long userId);

    /**
     * 查询当前用户的社交圈子列表
     * 包含用户创建的圈子和加入的圈子
     *
     * @param userId 用户ID
     * @return 社交圈子列表
     */
    List<SocialCircleInfoVO> getMtSocialCircleList(Long userId);

    /**
     * 条件搜索圈子列表（分页）
     * 支持按圈子名称、分类、地址等多维度条件进行搜索
     * 只返回审核通过且公开的圈子
     *
     * @param request 搜索条件和分页参数
     * @return 分页搜索结果
     */
    PageObj<SocialCircleSearchVO> searchCircles(SocialCircleSearchRequest request);

    /**
     * 查询普通成员在指定圈子中可访问的按钮权限列表
     * 通过递归查询圈子详情下的所有C类型菜单，然后查询这些菜单下的F类型按钮，
     * 最后与权限表匹配过滤出普通成员有权限的按钮
     * 
     * @param socialCircleId 圈子ID
     * @param userId 用户ID（用于权限验证）
     * @return 普通成员可访问的按钮权限列表
     */
    List<CircleFunctionMenuVO> getMemberButtonPermissions(Long socialCircleId, Long userId);

}
