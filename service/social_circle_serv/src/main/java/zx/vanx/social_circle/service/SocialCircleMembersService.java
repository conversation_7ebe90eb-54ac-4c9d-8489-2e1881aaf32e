package zx.vanx.social_circle.service;

import zx.vanx.social_circle.entity.SocialCircleMembers;
import com.baomidou.mybatisplus.extension.service.IService;
import zx.vanx.social_circle.request.SocialCircleMembersPageRequest;
import zx.vanx.social_circle.vo.SocialCircleMembersVO;
import zx.vanx.common_util.core.page.PageObj;

import java.util.List;

/**
 * <p>
 * 圈子成员表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface SocialCircleMembersService extends IService<SocialCircleMembers> {

    /**
     * 分页查询指定圈子的成员信息
     * 支持按成员昵称搜索、角色过滤、状态过滤等条件
     * 返回结果按角色优先级排序（圈主 > 管理员 > 普通成员）
     *
     * @param request 分页查询请求参数
     * @param userId  当前用户ID
     * @return 分页结果
     */
    PageObj<SocialCircleMembersVO> getSocialCircleMembersPage(SocialCircleMembersPageRequest request, Long userId);

    /**
     * 申请加入圈子
     * 通过圈子编码查找圈子并创建申请记录
     * 支持重复申请检查和状态验证
     *
     * @param socialCircleCode 圈子唯一识别码
     * @param reasonForAdd     申请理由
     * @param userId           申请用户ID
     * @return 创建的申请记录
     */
    SocialCircleMembers applyJoinCircle(String socialCircleCode, String reasonForAdd, Long userId);

    /**
     * 批量审批圈子申请
     * 圈主和管理员可以批量审批用户的入圈申请
     * 支持同意和驳回操作
     *
     * @param memberIds      要审批的成员ID列表
     * @param approvalAction 审批操作类型（同意/驳回）
     * @param rejectReason   驳回理由（驳回时可选）
     * @param operatorUserId 操作者用户ID
     * @return 成功处理的记录数量
     */
    Integer batchApprovalMembers(List<Long> memberIds, String approvalAction, String rejectReason, Long operatorUserId);

    /**
     * 退出圈子
     * 用户主动退出已加入的圈子
     * 圈主不能直接退出，需要先转让圈主身份
     *
     * @param socialCircleId 圈子ID
     * @param leaveReason    退出理由（可选）
     * @param userId         退出用户ID
     * @return 更新后的成员记录
     */
    SocialCircleMembers leaveCircle(Long socialCircleId, String leaveReason, Long userId);

    /**
     * 圈主修改成员角色
     * 只有圈主才能修改其他成员的角色
     * 支持将成员设置为管理员或普通成员
     *
     * @param socialCircleId 圈子ID
     * @param memberId       要修改角色的成员用户ID
     * @param newRole        新角色（管理员/普通成员）
     * @param changeReason   修改原因（可选）
     * @param ownerUserId    操作者用户ID（必须是圈主）
     * @return 更新后的成员记录
     */
    SocialCircleMembers changeMemberRole(Long socialCircleId, Long memberId, String newRole, String changeReason, Long ownerUserId);

    /**
     * 获取圈子管理员列表
     * 查询指定圈子的所有管理员成员信息
     *
     * @param userId   当前用户ID
     * @param circleId 圈子ID
     * @return 管理员成员信息列表
     */
    List<SocialCircleMembersVO> getCircleManagers(Long userId, Long circleId);

    /**
     * 获取用户默认圈子的成员数量
     * 统计用户默认圈子中的成员总数
     *
     * @param userId 用户ID
     * @return 默认圈子成员数量
     */
    Long getDefaultCircleMemberCount(Long userId);
}
