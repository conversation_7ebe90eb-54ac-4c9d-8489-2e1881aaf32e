package zx.vanx.social_circle.service;

import zx.vanx.social_circle.entity.SocialCirclePostCategRelat;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 圈子帖子分类关联 服务类
 * 提供圈子与帖子分类的关联管理功能
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
public interface SocialCirclePostCategRelatService extends IService<SocialCirclePostCategRelat> {

    /**
     * 批量创建圈子帖子分类关联
     * 用于圈子创建时，建立圈子与其支持的帖子分类的关联关系
     * 
     * @param socialCircleId 圈子ID
     * @param postCategIds 帖子分类ID
     * @throws zx.vanx.common_util.exception.ZxException 业务异常（参数验证失败、数据库操作失败等）
     */
    void createCirclePostCategRelations(Long socialCircleId, List<Long> postCategIds);

    /**
     * 根据圈子ID查询关联的帖子分类
     * 用于查询指定圈子支持的所有帖子分类
     * 
     * @param socialCircleId 圈子ID
     * @return 关联的帖子分类列表，如果没有关联则返回空列表
     */
    List<SocialCirclePostCategRelat> getPostCategsByCircleId(Long socialCircleId);

    /**
     * 删除圈子的所有帖子分类关联
     * 用于圈子删除或重新设置帖子分类时
     * 
     * @param socialCircleId 圈子ID
     * @return 删除的记录数
     */
    int removePostCategsByCircleId(Long socialCircleId);

    /**
     * 检查圈子是否支持指定的帖子分类
     * 用于发帖时验证分类是否被该圈子允许
     * 
     * @param socialCircleId 圈子ID
     * @param postCategId 帖子分类ID
     * @return 如果圈子支持该分类返回true，否则返回false
     */
    boolean isPostCategSupportedByCircle(Long socialCircleId, Long postCategId);

}
