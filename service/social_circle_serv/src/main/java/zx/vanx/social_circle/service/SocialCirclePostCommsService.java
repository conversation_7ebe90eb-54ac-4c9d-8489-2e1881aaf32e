package zx.vanx.social_circle.service;

import com.baomidou.mybatisplus.extension.service.IService;
import zx.vanx.social_circle.entity.SocialCirclePostComms;
import zx.vanx.social_circle.request.SysObjectCommsAddRequest;
import zx.vanx.social_circle.vo.SysObjectCommsVo;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 用户系统对象的评论表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
public interface SocialCirclePostCommsService extends IService<SocialCirclePostComms> {

    /**
     * 添加评论
     * @param sysObjectCommsAddRequest 评论对象
     */
    SysObjectCommsVo addSysObjectComms(SysObjectCommsAddRequest sysObjectCommsAddRequest);

    /**
     * 删除评论
     * @param objectCommsId 评论ID
     * @param circlePostId 评论对象的id
     */
    void deleteSysObjectComms(Long objectCommsId,Long circlePostId,@NotNull Long userId);

    /**
     * 查询一级帖子评论，未分页
     * @param circlePostId 帖子ID
     * @param currentPage 当前页
     * @param limit 每页显示条数
     * @return 评论对象
     */
    List<SysObjectCommsVo> selectSysObjectComms(Long circlePostId,Long currentPage,Long limit);

    /**
     * 查询视频子评论
     * @param topCommsId 评论层次中的第一层次的评论id
     * @param currentPage 当前页
     * @param limit 每页显示条数
     * @return 评论对象
     */
    List<SysObjectCommsVo> selectSubSysObjectComms(Long topCommsId,Long currentPage,Long limit);

}
