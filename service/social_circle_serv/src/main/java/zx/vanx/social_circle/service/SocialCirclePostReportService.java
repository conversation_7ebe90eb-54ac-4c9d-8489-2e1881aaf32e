package zx.vanx.social_circle.service;

import zx.vanx.common_util.core.page.PageObj;
import zx.vanx.social_circle.entity.SocialCirclePostReport;
import zx.vanx.social_circle.request.PostReportPageRequest;
import zx.vanx.social_circle.request.PostReportRequest;
import zx.vanx.social_circle.vo.PostReportManagementVO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 帖子举报信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
public interface SocialCirclePostReportService extends IService<SocialCirclePostReport> {

    /**
     * 举报帖子
     * 同一用户不能重复举报同一帖子
     * 
     * @param request 举报请求参数
     * @param reportUserId 举报用户ID
     * @return 举报记录
     */
    SocialCirclePostReport reportPost(PostReportRequest request, Long reportUserId);

    /**
     * 检查用户是否已举报过该帖子
     * 
     * @param circlePostId 帖子ID
     * @param reportUserId 举报用户ID
     * @return 是否已举报
     */
    boolean hasUserReported(Long circlePostId, Long reportUserId);

    /**
     * 管理员分页查询举报清单
     * 聚合显示被举报帖子的详细信息和举报统计数据
     * 需要验证当前用户是否为指定圈子的圈主
     * 
     * @param request 分页查询请求参数
     * @param currentUserId 当前用户ID
     * @return 分页结果，包含被举报帖子的详细信息和举报统计
     */
    PageObj<PostReportManagementVO> getReportManagementList(PostReportPageRequest request, Long currentUserId);

}
