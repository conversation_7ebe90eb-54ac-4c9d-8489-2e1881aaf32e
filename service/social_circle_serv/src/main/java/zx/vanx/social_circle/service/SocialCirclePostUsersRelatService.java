package zx.vanx.social_circle.service;

import zx.vanx.social_circle.entity.SocialCirclePostUsersRelat;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * vanx_media_others_relat 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface SocialCirclePostUsersRelatService extends IService<SocialCirclePostUsersRelat> {

    /**
     * 根据帖子ID和用户ID获取用户帖子关系记录
     *
     * @param postId 帖子ID
     * @param userId 用户ID
     * @return 用户帖子关系记录，不存在则返回null
     */
    SocialCirclePostUsersRelat getByPostIdAndUserId(Long postId, Long userId);

}
