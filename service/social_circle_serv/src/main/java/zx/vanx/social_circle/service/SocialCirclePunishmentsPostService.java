package zx.vanx.social_circle.service;

import zx.vanx.social_circle.entity.SocialCirclePunishmentsPost;
import com.baomidou.mybatisplus.extension.service.IService;
import zx.vanx.social_circle.request.SocialCirclePunishmentsAddRequest;
import zx.vanx.social_circle.vo.SocialCirclePunishmentsPostVO;

/**
 * <p>
 * 圈子处罚记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
public interface SocialCirclePunishmentsPostService extends IService<SocialCirclePunishmentsPost> {

    /**
     * 新增用户处罚
     *
     * @param userId 进行处罚的用户id
     * @param publishRequest 处罚请求对象
     * @return 处罚记录的视图对象
     */
    SocialCirclePunishmentsPostVO addUserPunishments(Long userId, SocialCirclePunishmentsAddRequest publishRequest);
}
