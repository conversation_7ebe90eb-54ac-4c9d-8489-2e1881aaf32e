package zx.vanx.social_circle.service;

import zx.vanx.social_circle.entity.SocialCirclePunishmentsUser;
import zx.vanx.social_circle.vo.UserPunishmentsListVO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 圈子处罚记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface SocialCirclePunishmentsUserService extends IService<SocialCirclePunishmentsUser> {

    /**
     * 根据用户ID查询用户的处罚记录列表
     * 包含用户基本信息和处罚记录详情
     *
     * @param userId 用户ID
     * @return 用户处罚列表视图对象
     */
    UserPunishmentsListVO getUserPunishmentsList(Long userId);

}
