package zx.vanx.social_circle.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import zx.vanx.social_circle.entity.SocialCircleCategoryRelat;
import zx.vanx.social_circle.entity.SocialCirclePlatfCategory;
import zx.vanx.social_circle.mapper.SocialCircleCategoryRelatMapper;
import zx.vanx.social_circle.mapper.SocialCirclePlatfCategoryMapper;
import zx.vanx.social_circle.service.SocialCircleCategoryRelatService;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * vanx_media_categ_relat 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Service
public class SocialCircleCategoryRelatServiceImpl extends ServiceImpl<SocialCircleCategoryRelatMapper, SocialCircleCategoryRelat> implements SocialCircleCategoryRelatService {

    @Autowired
    private SocialCirclePlatfCategoryMapper categoryMapper;

    /**
     * 获取平台圈子分类树
     *
     * @return SocialCirclePlatfCategory
     */
    @Override
    public List<SocialCirclePlatfCategory> getCategTree() {

        // 1. 查询所有分类
        List<SocialCirclePlatfCategory> allCategories = categoryMapper.selectList(null);

        // 2. 构建树形结构
                List<SocialCirclePlatfCategory> rootCategories = new ArrayList<>();
                for (SocialCirclePlatfCategory category : allCategories) {
                    if (category.getParentSocialCircleCategId() == null || category.getParentSocialCircleCategId() == 0) {
                        // 找到根节点
                        rootCategories.add(category);
                        // 递归查找子节点
                        findChildren(category, allCategories);
                    }
                }

        // 3. 返回树形数据
        return rootCategories;
    }

    /**
     * 递归查找子分类
     * @param parentCategory 父分类
     * @param allCategories 所有分类列表
     */
    private void findChildren(SocialCirclePlatfCategory parentCategory, List<SocialCirclePlatfCategory> allCategories) {
        List<SocialCirclePlatfCategory> children = new ArrayList<>();
        for (SocialCirclePlatfCategory category : allCategories) {
            if (parentCategory.getSocialCircleCategId().equals(category.getParentSocialCircleCategId())) {
                children.add(category);
                // 递归查找子节点
                findChildren(category, allCategories);
            }
        }
        parentCategory.setChildren(children);
    }

}
