package zx.vanx.social_circle.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import zx.vanx.common_util.exception.ZxException;
import zx.vanx.social_circle.entity.PlatfCircleFunction;
import zx.vanx.social_circle.entity.SocialCircleFuncRelat;
import zx.vanx.social_circle.mapper.PlatfCircleFunctionMapper;
import zx.vanx.social_circle.mapper.SocialCircleFuncRelatMapper;
import zx.vanx.social_circle.service.SocialCircleFuncRelatService;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 圈子菜单权限功能表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Service
@RequiredArgsConstructor
public class SocialCircleFuncRelatServiceImpl extends ServiceImpl<SocialCircleFuncRelatMapper, SocialCircleFuncRelat> implements SocialCircleFuncRelatService {

    private final PlatfCircleFunctionMapper platfCircleFunctionMapper;

    /**
     * 为圈主创建菜单功能权限关联
     * @param socialCircleId 圈子ID
     * @param selectedFunctionIds 选中的圈子详情菜单ID列表
     */
    @Override
    public void createOwnerFunctionRelations(Long socialCircleId, List<Long> selectedFunctionIds) {
        // 1. 参数验证
        validateCreateParams(socialCircleId,selectedFunctionIds);
        
        // 2. 获取所有需要关联的菜单ID（包含圈子详情选中菜单、圈子管理菜单、关联菜单菜单）

        // 2.1 添加选中的圈子详情菜单吗，

        // 2.2 添加圈子管理下的所有子菜单（递归查询，排除圈子管理本身）
        List<PlatfCircleFunction> managementFunctions = platfCircleFunctionMapper.selectAllSubMenusRecursive("圈子管理");
        Set<Long> allFunctionIds = managementFunctions.stream().map(PlatfCircleFunction::getFunctionId).collect(Collectors.toSet());
        
        // 2.3 添加关联菜单
        List<PlatfCircleFunction> associatedFunctions = platfCircleFunctionMapper.selectAssociatedFunctions(selectedFunctionIds);
        allFunctionIds.addAll(associatedFunctions.stream().map(PlatfCircleFunction::getFunctionId).collect(Collectors.toList()));
        
        // 3. 创建圈主权限关联记录
        createFunctionRelations(socialCircleId,  "圈主", new ArrayList<>(allFunctionIds));

    }

    /**
     * 为普通成员创建菜单功能权限关联
     * @param socialCircleId 圈子ID
     * @param selectedFunctionIds 选中的圈子详情菜单ID列表
     */
    @Override
    public void createMemberFunctionRelations(Long socialCircleId, List<Long> selectedFunctionIds) {
        // 1. 参数验证
        validateCreateParams(socialCircleId, selectedFunctionIds);
        
        // 2. 获取需要关联的菜单ID（圈子详情选中菜单）
        Set<Long> allFunctionIds = new HashSet<>(selectedFunctionIds);
        
        // 3. 创建普通成员权限关联记录
        createFunctionRelations(socialCircleId, "普通成员", new ArrayList<>(allFunctionIds));


    }

    /**
     * 参数验证
     */
    private void validateCreateParams(Long socialCircleId, List<Long> selectedFunctionIds) {
        if (socialCircleId == null) {
            throw new ZxException(400, "圈子ID不能为空");
        }

        if (CollectionUtils.isEmpty(selectedFunctionIds)) {
            throw new ZxException(400, "选中的功能菜单ID列表不能为空");
        }
    }

    /**
     * 创建菜单功能权限关联记录
     */
    private void createFunctionRelations(Long socialCircleId,  String roleName, List<Long> functionIds) {

        List<SocialCircleFuncRelat> relations = new ArrayList<>();
        
        for (Long functionId : functionIds) {
            SocialCircleFuncRelat relation = buildFunctionRelation(socialCircleId,  roleName, functionId);
            relations.add(relation);
        }
        
        // 批量保存
        boolean saveResult = this.saveBatch(relations);
        if (!saveResult) {
            throw new ZxException(500, "创建菜单权限关联失败");
        }
    }

    /**
     * 构建菜单功能权限关联对象
     */
    private SocialCircleFuncRelat buildFunctionRelation(Long socialCircleId, String roleName, Long functionId) {

        SocialCircleFuncRelat relation = new SocialCircleFuncRelat();
        relation.setSocialCircleId(socialCircleId);
        relation.setRoleName(roleName);
        relation.setFunctionId(functionId);
        
        // 设置系统字段
        LocalDateTime now = LocalDateTime.now();
        relation.setCreatedTime(now);
        relation.setModifiedTime(now);
        
        return relation;
    }
}
