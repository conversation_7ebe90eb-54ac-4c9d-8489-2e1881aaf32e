package zx.vanx.social_circle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import zx.vanx.common_util.core.page.PageObj;
import zx.vanx.common_util.core.redis.RedisCache;
import zx.vanx.common_util.exception.ZxException;
import zx.vanx.social_circle.entity.SocialCircleInfo;
import zx.vanx.social_circle.entity.SocialCircleMembers;
import zx.vanx.social_circle.mapper.SocialCircleInfoMapper;
import zx.vanx.social_circle.mapper.SocialCircleMembersMapper;
import zx.vanx.social_circle.request.SocialCircleMembersPageRequest;
import zx.vanx.social_circle.service.SocialCircleMembersService;
import zx.vanx.social_circle.vo.SocialCircleMembersVO;
import zx.vanx.user_permiss.redis.key.PermissKey;
import zx.vanx.user_permiss.vo.UserDetail;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 圈子成员表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
@RequiredArgsConstructor
public class SocialCircleMembersServiceImpl extends ServiceImpl<SocialCircleMembersMapper, SocialCircleMembers> implements SocialCircleMembersService {

    /**
     * Redis缓存
     */
    private final RedisCache redisCache;

    /**
     * 社交圈子信息表 Mapper 接口
     */
    private final SocialCircleInfoMapper socialCircleInfoMapper;

    /**
     * 分页查询指定圈子的成员信息
     * 支持按成员昵称搜索、角色过滤、状态过滤等条件
     * 返回结果按角色优先级排序（圈主 > 管理员 > 普通成员）
     *
     * @param request 分页查询请求参数
     * @param userId  当前用户ID
     * @return 分页结果
     */
    @Override
    public PageObj<SocialCircleMembersVO> getSocialCircleMembersPage(SocialCircleMembersPageRequest request, Long userId) {
        
        // 参数校验
        if (request == null || request.getCircleId() == null) {
            PageObj<SocialCircleMembersVO> emptyResult = new PageObj<>();
            emptyResult.setTotal(0L);
            emptyResult.setRecords(new ArrayList<>());
            return emptyResult;
        }

        // 设置默认分页参数
        if (request.getPage() == null || request.getPage() < 1) {
            request.setPage(1L);
        }
        if (request.getLimit() == null || request.getLimit() < 1) {
            request.setLimit(10L);
        }

        // 构建查询条件
        LambdaQueryWrapper<SocialCircleMembers> queryWrapper = buildMembersQueryWrapper(request);

        // 创建分页对象
        Page<SocialCircleMembers> page = new Page<>(request.getPage(), request.getLimit());

        // 执行分页查询
        IPage<SocialCircleMembers> pageResult = this.page(page, queryWrapper);

        // 获取成员列表并组装VO
        List<SocialCircleMembers> members = pageResult.getRecords();
        List<SocialCircleMembersVO> membersVOList = assembleCircleMembersVO(members);

        // 构建分页结果
        PageObj<SocialCircleMembersVO> result = new PageObj<>();
        result.setTotal(pageResult.getTotal());
        result.setRecords(membersVOList);

        return result;
    }

    /**
     * 构建成员分页查询条件
     * 支持按圈子ID、成员昵称、角色等条件查询
     * 默认按加入时间降序排序
     *
     * @param request 分页查询请求参数
     * @return 查询条件构造器
     */
    private LambdaQueryWrapper<SocialCircleMembers> buildMembersQueryWrapper(SocialCircleMembersPageRequest request) {
        LambdaQueryWrapper<SocialCircleMembers> queryWrapper = new LambdaQueryWrapper<>();
        
        // 必填条件：圈子ID
        queryWrapper.eq(SocialCircleMembers::getSocialCircleId, request.getCircleId());
        
        // 固定条件：只查询已入圈的成员
        queryWrapper.eq(SocialCircleMembers::getUserStatus, "已入圈");
        
        // 可选条件：成员角色过滤
        if (StringUtils.hasText(request.getUserRole())) {
            queryWrapper.eq(SocialCircleMembers::getUserRole, request.getUserRole());
        }
        
        // 可选条件：成员昵称模糊搜索
        if (StringUtils.hasText(request.getMemberName())) {
            queryWrapper.like(SocialCircleMembers::getUserRemarkName, request.getMemberName());
        }
        
        // 默认排序：按加入时间降序排序
        queryWrapper.orderByDesc(SocialCircleMembers::getCreatedTime);
        
        return queryWrapper;
    }


    /**
     * 组装圈子成员VO列表并按角色优先级排序
     * @param members 成员列表
     * @return 圈子成员VO列表
     */
    private List<SocialCircleMembersVO> assembleCircleMembersVO(List<SocialCircleMembers> members) {
        // 如果成员列表为空，则返回空列表
        if (CollectionUtils.isEmpty(members)) {
            return Collections.emptyList();
        }

        // 创建结果列表
        List<SocialCircleMembersVO> result = new ArrayList<>();

        for (SocialCircleMembers member : members) {

            // 创建VO对象
            SocialCircleMembersVO vo = new SocialCircleMembersVO();

            // 基本信息
            vo.setUserId(member.getUserId()); // 用户ID
            vo.setUserRemarkName(member.getUserRemarkName()); // 用户备注名
            vo.setUserRole(member.getUserRole());

            // 从Redis获取用户详细信息
            UserDetail userDetail = redisCache.getCacheObject(PermissKey.USER_DETAIL + member.getUserId());

            // 如果用户详细信息不为空，则设置用户昵称和头像URL
            if (userDetail != null) {
                // 在实际项目中，可能需要调用其他服务获取这些信息
                vo.setUserNickName(userDetail.getUserInfo().getUserNickname()); // 需要从其他地方获取用户昵称
                vo.setUserPhotoUrl(userDetail.getUserAvatar().getPictureUrl()); // 需要从其他地方获取用户头像URL
            }

            // 将VO对象添加到结果列表中
            result.add(vo);
        }

        // 按角色优先级排序：圈主 > 管理员 > 普通成员
        result.sort((m1, m2) -> {
            // 获取第一个成员的角色优先级
            int priority1 = getRolePriority(m1.getUserRole());
            // 获取第二个成员的角色优先级
            int priority2 = getRolePriority(m2.getUserRole());
            // 按优先级降序排序（优先级高的在前面）
            return Integer.compare(priority2, priority1);
        });

        return result;
    }

    /**
     * 获取用户角色的优先级
     * 用于成员列表排序
     *
     * @param userRole 用户角色
     * @return 优先级数值，数值越大优先级越高
     */
    private int getRolePriority(String userRole) {
        if ("圈主".equals(userRole)) {
            return 3;
        } else if ("管理员".equals(userRole)) {
            return 2;
        } else if ("普通成员".equals(userRole)) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * 申请加入圈子
     * 通过圈子编码查找圈子并创建申请记录
     * 支持重复申请检查和状态验证
     *
     * @param socialCircleCode 圈子唯一识别码
     * @param reasonForAdd     申请理由
     * @param userId           申请用户ID
     * @return 创建的申请记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SocialCircleMembers applyJoinCircle(String socialCircleCode, String reasonForAdd, Long userId) {
        
        // 1. 参数验证
        if (!StringUtils.hasText(socialCircleCode)) {
            throw new ZxException(400, "圈子编码不能为空");
        }
        if (userId == null) {
            throw new ZxException(400, "用户ID不能为空");
        }
        
        // 2. 通过圈子编码查询圈子信息
        SocialCircleInfo circleInfo = findCircleByCode(socialCircleCode);
        
        // 3. 验证圈子状态
        validateCircleStatus(circleInfo);
        
        // 4. 检查用户是否已经是成员或有待审核申请
        checkDuplicateApplication(circleInfo.getSocialCircleId(), userId);
        
        // 5. 创建申请记录
        return createApplicationRecord(circleInfo.getSocialCircleId(), userId, reasonForAdd);
    }

    /**
     * 批量审批圈子申请
     * 圈主和管理员可以批量审批用户的入圈申请
     * 支持同意和驳回操作
     *
     * @param memberIds      要审批的成员ID列表
     * @param approvalAction 审批操作类型（同意/驳回）
     * @param rejectReason   驳回理由（驳回时可选）
     * @param operatorUserId 操作者用户ID
     * @return 成功处理的记录数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchApprovalMembers(List<Long> memberIds, String approvalAction, String rejectReason, Long operatorUserId) {
        
        // 1. 参数验证
        if (CollectionUtils.isEmpty(memberIds)) {
            throw new ZxException(400, "成员ID列表不能为空");
        }
        if (!StringUtils.hasText(approvalAction)) {
            throw new ZxException(400, "审批操作类型不能为空");
        }
        if (operatorUserId == null) {
            throw new ZxException(400, "操作者用户ID不能为空");
        }
        
        // 2. 查询待审批的成员记录
        List<SocialCircleMembers> pendingMembers = queryPendingMembers(memberIds);
        
        if (CollectionUtils.isEmpty(pendingMembers)) {
            throw new ZxException(400, "没有找到待审批的申请记录");
        }
        
        // 3. 验证操作者权限（以第一个申请记录的圈子为准）
        Long circleId = pendingMembers.get(0).getSocialCircleId();
        validateOperatorPermission(circleId, operatorUserId);
        
        // 4. 执行批量审批
        return performBatchApproval(pendingMembers, approvalAction, rejectReason, operatorUserId);
    }

    /**
     * 通过圈子编码查询圈子信息
     */
    private SocialCircleInfo findCircleByCode(String socialCircleCode) {
        LambdaQueryWrapper<SocialCircleInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCircleInfo::getSocialCircleCode, socialCircleCode);

        SocialCircleInfo circleInfo = socialCircleInfoMapper.selectOne(queryWrapper);
        if (circleInfo == null) {
            throw new ZxException(404, "圈子不存在，请检查圈子编码是否正确");
        }
        
        return circleInfo;
    }

    /**
     * 验证圈子状态
     */
    private void validateCircleStatus(SocialCircleInfo circleInfo) {
        if (!"开通".equals(circleInfo.getSocialCircleStatus())) {
            throw new ZxException(400, "该圈子尚未开通，暂不支持申请加入");
        }
        
        if (!"公开".equals(circleInfo.getVisibilityStatus())) {
            throw new ZxException(400, "该圈子为私有圈子，暂不支持申请加入");
        }
    }

    /**
     * 检查重复申请
     */
    private void checkDuplicateApplication(Long circleId, Long userId) {
        LambdaQueryWrapper<SocialCircleMembers> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCircleMembers::getSocialCircleId, circleId);
        queryWrapper.eq(SocialCircleMembers::getUserId, userId);
        queryWrapper.in(SocialCircleMembers::getUserStatus, "申请", "已入圈");
        
        SocialCircleMembers existingMember = this.getOne(queryWrapper);
        if (existingMember != null) {
            if ("申请".equals(existingMember.getUserStatus())) {
                throw new ZxException(400, "您已提交过申请，请等待审核，请勿重复申请");
            } else if ("已入圈".equals(existingMember.getUserStatus())) {
                throw new ZxException(400, "您已经是该圈子的成员，无需重复申请");
            }
        }
    }

    /**
     * 创建申请记录
     */
    private SocialCircleMembers createApplicationRecord(Long circleId, Long userId, String reasonForAdd) {
        SocialCircleMembers member = new SocialCircleMembers();
        
        // 设置基本信息
        member.setSocialCircleId(circleId);
        member.setUserId(userId);
        member.setUserRole("普通成员");  // 申请时默认为普通成员
        member.setUserStatus("申请");    // 该成员在圈子中的状态：申请、已入圈、驳回、已退圈
        member.setUserJoinStatus("正常");    // 该成员入圈后的状态：正常、警告、临时禁言、封禁
        member.setUserCircleRelatAddSource("主动加入");
        member.setIsDefault(false);     // 默认不是默认圈子
        member.setReasonForAdd(reasonForAdd);
        
        // 设置系统字段
        LocalDateTime now = LocalDateTime.now();
        member.setCreatedTime(now);
        member.setModifiedTime(now);

        // 保存申请记录
        boolean saveResult = this.save(member);
        if (!saveResult) {
            throw new ZxException(500, "申请提交失败，请稍后重试");
        }
        
        return member;
    }

    /**
     * 查询待审批的成员记录
     */
    private List<SocialCircleMembers> queryPendingMembers(List<Long> memberIds) {
        LambdaQueryWrapper<SocialCircleMembers> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SocialCircleMembers::getCircleMemberId, memberIds);
        queryWrapper.eq(SocialCircleMembers::getUserStatus, "申请");

        return this.list(queryWrapper);
    }

    /**
     * 验证操作者权限
     */
    private void validateOperatorPermission(Long circleId, Long operatorUserId) {

        LambdaQueryWrapper<SocialCircleMembers> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCircleMembers::getSocialCircleId, circleId);
        queryWrapper.eq(SocialCircleMembers::getUserId, operatorUserId);
        queryWrapper.eq(SocialCircleMembers::getUserStatus, "已入圈");
        queryWrapper.in(SocialCircleMembers::getUserRole, "圈主", "管理员");
        
        SocialCircleMembers operatorMember = this.getOne(queryWrapper);
        if (operatorMember == null) {
            throw new ZxException(403, "您没有权限进行此操作，只有圈主和管理员可以审批申请");
        }
    }

    /**
     * 执行批量审批
     */
    private Integer performBatchApproval(List<SocialCircleMembers> pendingMembers, String approvalAction, String rejectReason, Long operatorUserId) {
        int successCount = 0;
        LocalDateTime now = LocalDateTime.now();
        
        for (SocialCircleMembers member : pendingMembers) {
            try {
                if ("同意".equals(approvalAction)) {
                    // 同意申请：更新状态为已入圈
                    member.setUserStatus("已入圈");
                } else if ("驳回".equals(approvalAction)) {
                    // 驳回申请：更新状态为驳回，记录驳回理由
                    member.setUserStatus("驳回");
                    if (StringUtils.hasText(rejectReason)) {
                        member.setReasonForAdd(rejectReason);  // 临时使用这个字段存储驳回理由
                    }
                }
                
                // 更新审批信息
                member.setModifiedTime(now);
                member.setEditorId(operatorUserId);
                
                // 保存更新
                boolean updateResult = this.updateById(member);
                if (updateResult) {
                    successCount++;
                }
                
            } catch (Exception e) {
                // 记录错误日志，但不中断整个批量操作
                // 可以考虑记录到日志系统或返回详细的错误信息
                continue;
            }
        }
        
        return successCount;
    }


    /**
     * 退出圈子
     * 用户主动退出已加入的圈子
     * 圈主不能直接退出，需要先转让圈主身份
     *
     * @param socialCircleId 圈子ID
     * @param leaveReason    退出理由（可选）
     * @param userId         退出用户ID
     * @return 更新后的成员记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SocialCircleMembers leaveCircle(Long socialCircleId, String leaveReason, Long userId) {
        
        // 1. 参数验证
        if (socialCircleId == null) {
            throw new ZxException(400, "圈子ID不能为空");
        }
        if (userId == null) {
            throw new ZxException(400, "用户ID不能为空");
        }
        
        // 2. 查询用户在该圈子的成员记录
        SocialCircleMembers memberRecord = findMemberRecord(socialCircleId, userId);
        
        // 3. 验证成员状态和角色权限
        validateLeavePermission(memberRecord);
        
        // 4. 执行退出操作
        return performLeaveOperation(memberRecord, leaveReason);
    }

    /**
     * 查询用户在圈子的成员记录
     */
    private SocialCircleMembers findMemberRecord(Long socialCircleId, Long userId) {

        LambdaQueryWrapper<SocialCircleMembers> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCircleMembers::getSocialCircleId, socialCircleId);
        queryWrapper.eq(SocialCircleMembers::getUserId, userId);

        SocialCircleMembers memberRecord = this.getOne(queryWrapper);

        if (memberRecord == null) {
            throw new ZxException(404, "您不是该圈子的成员，无法退出");
        }
        
        return memberRecord;

    }

    /**
     * 验证退出权限
     */
    private void validateLeavePermission(SocialCircleMembers memberRecord) {
        // 验证成员状态
        if (!"已入圈".equals(memberRecord.getUserStatus())) {
            String currentStatus = memberRecord.getUserStatus();
            if ("申请".equals(currentStatus)) {
                throw new ZxException(400, "您的申请还在审核中，可以取消申请");
            } else if ("驳回".equals(currentStatus)) {
                throw new ZxException(400, "您的申请已被驳回，无需退出");
            } else if ("已退圈".equals(currentStatus)) {
                throw new ZxException(400, "您已经退出该圈子");
            } else {
                throw new ZxException(400, "当前状态不允许退出圈子");
            }
        }
        
        // 验证角色权限 - 圈主不能直接退出
        if ("圈主".equals(memberRecord.getUserRole())) {
            throw new ZxException(403, "圈主不能直接退出圈子，请先转让圈主身份给其他成员");
        }

    }

    /**
     * 执行退出操作
     */
    private SocialCircleMembers performLeaveOperation(SocialCircleMembers memberRecord, String leaveReason) {

        LocalDateTime now = LocalDateTime.now();
        
        // 更新成员状态
        memberRecord.setUserStatus("已退圈");
        
        // 如果是默认圈子，取消默认设置
        if (Boolean.TRUE.equals(memberRecord.getIsDefault())) {
            memberRecord.setIsDefault(false);
        }
        
        // 记录退出理由（使用reasonForAdd字段临时存储）
        if (StringUtils.hasText(leaveReason)) {
            memberRecord.setReasonForAdd("退出理由：" + leaveReason);
        }
        
        // 更新时间信息
        memberRecord.setModifiedTime(now);
        memberRecord.setEditorId(memberRecord.getUserId());
        
        // 保存更新
        boolean updateResult = this.updateById(memberRecord);
        if (!updateResult) {
            throw new ZxException(500, "退出圈子失败，请稍后重试");
        }
        
        return memberRecord;
    }

    /**
     * 圈主修改成员角色
     * 只有圈主才能修改其他成员的角色
     * 支持将成员设置为管理员或普通成员
     *
     * @param socialCircleId 圈子ID
     * @param memberId       要修改角色的成员用户ID
     * @param newRole        新角色（管理员/普通成员）
     * @param changeReason   修改原因（可选）
     * @param ownerUserId    操作者用户ID（必须是圈主）
     * @return 更新后的成员记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SocialCircleMembers changeMemberRole(Long socialCircleId, Long memberId, String newRole, String changeReason, Long ownerUserId) {
        
        // 1. 参数验证
        validateRoleChangeParams(socialCircleId, memberId, newRole, ownerUserId);
        
        // 2. 验证操作者是否为圈主
        SocialCircleMembers ownerRecord = validateOwnerPermission(socialCircleId, ownerUserId);
        
        // 3. 查询并验证目标成员
        SocialCircleMembers targetMember = validateTargetMember(socialCircleId, memberId);
        
        // 4. 验证角色修改业务规则
        validateRoleChangeRules(targetMember, newRole, ownerUserId);
        
        // 5. 执行角色修改
        return performRoleChange(targetMember, newRole, changeReason, ownerUserId);

    }

    /**
     * 获取圈子管理员列表
     * 查询指定圈子的所有管理员成员信息
     *
     * @param userId   当前用户ID
     * @param circleId 圈子ID
     * @return 管理员成员信息列表
     */
    @Override
    public List<SocialCircleMembersVO> getCircleManagers(Long userId, Long circleId) {
        // 1. 参数验证
        if (userId == null) {
            throw new ZxException(400, "用户ID不能为空");
        }
        if (circleId == null) {
            throw new ZxException(400, "圈子ID不能为空");
        }
        // 2. 查询圈子成员列表
        LambdaQueryWrapper<SocialCircleMembers> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCircleMembers::getSocialCircleId, circleId)
                .eq(SocialCircleMembers::getUserStatus, "已入圈") // 只查询已入圈的成员
                .eq(SocialCircleMembers::getUserRole, "管理员") // 只查询管理员角色
                .orderByDesc(SocialCircleMembers::getCreatedTime); // 按创建时间降序排列
        List<SocialCircleMembers> members = this.baseMapper.selectList(queryWrapper);
        // 3. 组装管理员VO列表
        if (!CollectionUtils.isEmpty(members)) {
            List<SocialCircleMembersVO> managerVOs = new ArrayList<>();
            for (SocialCircleMembers member : members) {
                SocialCircleMembersVO vo = new SocialCircleMembersVO();
                vo.setUserId(member.getUserId());
                vo.setUserRole(member.getUserRole());
                vo.setUserRemarkName(member.getUserRemarkName());

                // 从Redis获取用户详细信息
                UserDetail userDetail = redisCache.getCacheObject(PermissKey.USER_DETAIL + member.getUserId());
                if (userDetail != null) {
                    vo.setUserNickName(userDetail.getUserInfo().getUserNickname());
                    vo.setUserPhotoUrl(userDetail.getUserAvatar().getPictureUrl());
                }

                managerVOs.add(vo);
            }
            return managerVOs;
        }
        return Collections.emptyList();
    }

    /**
     * 验证角色修改参数
     */
    private void validateRoleChangeParams(Long socialCircleId, Long memberId, String newRole, Long ownerUserId) {

        if (socialCircleId == null) {
            throw new ZxException(400, "圈子ID不能为空");
        }
        if (memberId == null) {
            throw new ZxException(400, "成员ID不能为空");
        }
        if (!StringUtils.hasText(newRole)) {
            throw new ZxException(400, "新角色不能为空");
        }
        if (ownerUserId == null) {
            throw new ZxException(400, "操作者ID不能为空");
        }
        
        // 验证角色值是否合法
        if (!"管理员".equals(newRole) && !"普通成员".equals(newRole)) {
            throw new ZxException(400, "新角色只能是管理员或普通成员");
        }

    }

    /**
     * 验证操作者是否为圈主
     */
    private SocialCircleMembers validateOwnerPermission(Long socialCircleId, Long ownerUserId) {

        LambdaQueryWrapper<SocialCircleMembers> ownerQuery = new LambdaQueryWrapper<>();
        ownerQuery.eq(SocialCircleMembers::getSocialCircleId, socialCircleId);
        ownerQuery.eq(SocialCircleMembers::getUserId, ownerUserId);
        ownerQuery.eq(SocialCircleMembers::getUserRole, "圈主");
        ownerQuery.eq(SocialCircleMembers::getUserStatus, "已入圈");
        ownerQuery.eq(SocialCircleMembers::getIsDeleted, 0);
        
        SocialCircleMembers ownerRecord = this.getOne(ownerQuery);

        if (ownerRecord == null) {
            throw new ZxException(403, "只有圈主才能修改成员角色");
        }
        
        return ownerRecord;
    }

    /**
     * 查询并验证目标成员
     */
    private SocialCircleMembers validateTargetMember(Long socialCircleId, Long memberId) {

        LambdaQueryWrapper<SocialCircleMembers> memberQuery = new LambdaQueryWrapper<>();
        memberQuery.eq(SocialCircleMembers::getSocialCircleId, socialCircleId);
        memberQuery.eq(SocialCircleMembers::getUserId, memberId);

        SocialCircleMembers targetMember = this.getOne(memberQuery);
        if (targetMember == null) {
            throw new ZxException(404, "指定的成员不存在");
        }
        
        // 验证成员状态
        if (!"已入圈".equals(targetMember.getUserStatus())) {
            throw new ZxException(400, "只能修改已入圈成员的角色，当前成员状态：" + targetMember.getUserStatus());
        }
        
        return targetMember;
    }

    /**
     * 验证角色修改业务规则
     */
    private void validateRoleChangeRules(SocialCircleMembers targetMember, String newRole, Long ownerUserId) {

        // 不能修改自己的角色
        if (targetMember.getUserId().equals(ownerUserId)) {
            throw new ZxException(400, "不能修改自己的角色");
        }
        
        // 不能修改圈主角色
        if ("圈主".equals(targetMember.getUserRole())) {
            throw new ZxException(400, "不能修改圈主的角色，如需转让圈主请使用专门的转让功能");
        }
        
        // 检查角色是否已经是目标角色
        if (newRole.equals(targetMember.getUserRole())) {
            throw new ZxException(400, "该成员已经是" + newRole + "角色，无需修改");
        }
    }

    /**
     * 执行角色修改操作
     */
    private SocialCircleMembers performRoleChange(SocialCircleMembers targetMember, String newRole, String changeReason, Long ownerUserId) {

        LocalDateTime now = LocalDateTime.now();
        
        // 记录原角色（用于日志）
        String originalRole = targetMember.getUserRole();
        
        // 更新角色
        targetMember.setUserRole(newRole);
        
        // 记录修改原因（如果提供）
        if (StringUtils.hasText(changeReason)) {
            String reasonLog = String.format("角色修改：%s → %s，原因：%s", originalRole, newRole, changeReason);
            targetMember.setReasonForAdd(reasonLog);
        }
        
        // 更新时间信息
        targetMember.setModifiedTime(now);
        targetMember.setEditorId(ownerUserId);
        
        // 保存更新
        boolean updateResult = this.updateById(targetMember);
        if (!updateResult) {
            throw new ZxException(500, "角色修改失败，请稍后重试");
        }
        
        return targetMember;
    }

    /**
     * 获取用户默认圈子的成员数量
     * 统计用户默认圈子中的成员总数
     *
     * @param userId 用户ID
     * @return 默认圈子成员数量
     */
    @Override
    public Long getDefaultCircleMemberCount(Long userId) {
        if (userId == null) {
            return 0L;
        }
        
        try {
            // 1. 先查询用户的默认圈子ID
            LambdaQueryWrapper<SocialCircleMembers> defaultCircleQuery = new LambdaQueryWrapper<>();
            defaultCircleQuery.eq(SocialCircleMembers::getUserId, userId)
                    .eq(SocialCircleMembers::getIsDefault, true)
                    .eq(SocialCircleMembers::getUserStatus, "已入圈")
                    .eq(SocialCircleMembers::getIsDeleted, 0);
            
            SocialCircleMembers defaultMember = this.getOne(defaultCircleQuery);
            if (defaultMember == null) {
                return 0L;
            }
            
            // 2. 统计该默认圈子的成员数量
            LambdaQueryWrapper<SocialCircleMembers> countQuery = new LambdaQueryWrapper<>();
            countQuery.eq(SocialCircleMembers::getSocialCircleId, defaultMember.getSocialCircleId())
                    .eq(SocialCircleMembers::getUserStatus, "已入圈")
                    .eq(SocialCircleMembers::getIsDeleted, 0);
            
            return this.count(countQuery);
            
        } catch (Exception e) {
            return 0L;
        }
    }

}
