package zx.vanx.social_circle.service.impl;

import zx.vanx.social_circle.entity.SocialCirclePlatfCategory;
import zx.vanx.social_circle.mapper.SocialCirclePlatfCategoryMapper;
import zx.vanx.social_circle.service.SocialCirclePlatfCategoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * vanx_platf_item_category 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
public class SocialCirclePlatfCategoryServiceImpl extends ServiceImpl<SocialCirclePlatfCategoryMapper, SocialCirclePlatfCategory> implements SocialCirclePlatfCategoryService {

}
