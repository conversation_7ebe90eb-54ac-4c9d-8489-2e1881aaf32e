package zx.vanx.social_circle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import zx.vanx.common_util.exception.ZxException;
import zx.vanx.social_circle.entity.SocialCirclePostCategRelat;
import zx.vanx.social_circle.mapper.SocialCirclePostCategRelatMapper;
import zx.vanx.social_circle.service.SocialCirclePostCategRelatService;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 圈子帖子分类关联 服务实现类
 * 提供圈子与帖子分类的关联管理功能
 * 支持批量创建、查询、删除等核心业务操作
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Service
@RequiredArgsConstructor
public class SocialCirclePostCategRelatServiceImpl extends ServiceImpl<SocialCirclePostCategRelatMapper, SocialCirclePostCategRelat> implements SocialCirclePostCategRelatService {

    private final SocialCirclePostCategRelatMapper socialCirclePostCategRelatMapper;

    /**
     * 批量创建圈子帖子分类关联
     * 用于圈子创建时，建立圈子与其支持的帖子分类的关联关系
     *
     * @param socialCircleId 圈子ID
     * @param postCategIds 帖子分类ID列表
     * @throws ZxException 业务异常（参数验证失败、数据库操作失败等）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createCirclePostCategRelations(Long socialCircleId,List<Long> postCategIds) {
        // 参数验证
        if (socialCircleId == null) {
            throw new ZxException(400, "圈子ID不能为空");
        }
        
        if (CollectionUtils.isEmpty(postCategIds)) {
            throw new ZxException(400, "帖子分类ID不能为空");
        }

        for (Long postCategId : postCategIds) {
            // 构建关联实体列表
            SocialCirclePostCategRelat relation = new SocialCirclePostCategRelat();
            relation.setSocialCircleId(socialCircleId);
            relation.setPostCategId(postCategId);

            // 设置系统字段
            LocalDateTime now = LocalDateTime.now();
            relation.setCreatedTime(now);
            relation.setModifiedTime(now);
            relation.setIsDeleted(0); // 未删除

            // 批量插入关联记录
            socialCirclePostCategRelatMapper.insert(relation);
        }
    }

    /**
     * 根据圈子ID查询关联的帖子分类
     * 用于查询指定圈子支持的所有帖子分类
     *
     * @param socialCircleId 圈子ID
     * @return 关联的帖子分类列表，如果没有关联则返回空列表
     */
    @Override
    public List<SocialCirclePostCategRelat> getPostCategsByCircleId(Long socialCircleId) {
        // 参数验证
        if (socialCircleId == null) {
            return new ArrayList<>();
        }

        // 构建查询条件
        LambdaQueryWrapper<SocialCirclePostCategRelat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCirclePostCategRelat::getSocialCircleId, socialCircleId);
        queryWrapper.orderByAsc(SocialCirclePostCategRelat::getSortNumber); // 按排序指数升序
        queryWrapper.orderByAsc(SocialCirclePostCategRelat::getCreatedTime); // 时间升序作为第二排序条件

        return this.list(queryWrapper);
    }

    /**
     * 删除圈子的所有帖子分类关联
     * 用于圈子删除或重新设置帖子分类时
     *
     * @param socialCircleId 圈子ID
     * @return 删除的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removePostCategsByCircleId(Long socialCircleId) {
        // 参数验证
        if (socialCircleId == null) {
            return 0;
        }

        // 构建删除条件（逻辑删除）
        LambdaQueryWrapper<SocialCirclePostCategRelat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCirclePostCategRelat::getSocialCircleId, socialCircleId);
        queryWrapper.eq(SocialCirclePostCategRelat::getIsDeleted, 0); // 只删除未删除的记录

        // 执行逻辑删除
        SocialCirclePostCategRelat updateEntity = new SocialCirclePostCategRelat();
        updateEntity.setIsDeleted(1); // 标记为已删除
        updateEntity.setModifiedTime(LocalDateTime.now());

        return this.update(updateEntity, queryWrapper) ? Math.toIntExact(this.count(queryWrapper)) : 0;
    }

    /**
     * 检查圈子是否支持指定的帖子分类
     * 用于发帖时验证分类是否被该圈子允许
     *
     * @param socialCircleId 圈子ID
     * @param postCategId 帖子分类ID
     * @return 如果圈子支持该分类返回true，否则返回false
     */
    @Override
    public boolean isPostCategSupportedByCircle(Long socialCircleId, Long postCategId) {
        // 参数验证
        if (socialCircleId == null || postCategId == null) {
            return false;
        }

        // 查询是否存在关联记录
        LambdaQueryWrapper<SocialCirclePostCategRelat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCirclePostCategRelat::getSocialCircleId, socialCircleId);
        queryWrapper.eq(SocialCirclePostCategRelat::getPostCategId, postCategId);
        queryWrapper.eq(SocialCirclePostCategRelat::getIsDeleted, 0); // 只查询未删除的记录

        return this.count(queryWrapper) > 0;
    }

}