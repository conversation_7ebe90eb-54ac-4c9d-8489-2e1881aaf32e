package zx.vanx.social_circle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.core.redis.RedisCache;
import zx.vanx.common_util.utils.BeanUtil;
import zx.vanx.common_util.utils.PageHelper;
import zx.vanx.social_circle.entity.SocialCirclePostComms;
import zx.vanx.social_circle.mapper.SocialCirclePostCommsMapper;
import zx.vanx.social_circle.request.SysObjectCommsAddRequest;
import zx.vanx.social_circle.service.SocialCirclePostCommsService;
import zx.vanx.social_circle.vo.SysObjectCommsVo;
import zx.vanx.user_permiss.redis.key.PermissKey;
import zx.vanx.user_permiss.vo.UserDetail;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户系统对象的评论表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Service
@RequiredArgsConstructor
public class SocialCirclePostCommsServiceImpl extends ServiceImpl<SocialCirclePostCommsMapper, SocialCirclePostComms> implements SocialCirclePostCommsService {

    private final RedisCache redisCache;

    /**
     * 添加评论
     * @param sysObjectCommsAddRequest 评论对象
     */
    @Override
    public SysObjectCommsVo addSysObjectComms(SysObjectCommsAddRequest sysObjectCommsAddRequest) {

        // 对象拷贝
        SocialCirclePostComms socialCirclePostComms = BeanUtil.copyProperties(sysObjectCommsAddRequest, SocialCirclePostComms::new);

        // 添加到数据库
        socialCirclePostComms.setUserId(LoginHelper.getUserId());
        baseMapper.insert(socialCirclePostComms);

        // 更新帖子评论数
        this.updatePostCommentNum(sysObjectCommsAddRequest.getCommsedObjectId(),true);

        // 构建评论对象返回给前端
        return buildSysObjectCommsVo(socialCirclePostComms);
    }

    /**
     * 构建评论对象返回给前端
     * @param socialCirclePostComms 评论对象
     * @return 评论对象
     */
    private SysObjectCommsVo buildSysObjectCommsVo(SocialCirclePostComms socialCirclePostComms) {

        // 拷贝评论对象
        SysObjectCommsVo sysObjectCommsVo = BeanUtil.copyProperties(socialCirclePostComms, SysObjectCommsVo::new);
        // 获取评论用户信息
        UserDetail userDetail = redisCache.getCacheObject(PermissKey.USER_DETAIL + socialCirclePostComms.getUserId());

        // 获取父级评论用户信息
        if (!ObjectUtils.isEmpty(socialCirclePostComms.getParentCommsUserId())) {
            UserDetail userParentDetail = redisCache
                    .getCacheObject(PermissKey.USER_DETAIL + socialCirclePostComms.getParentCommsUserId());
            if (!ObjectUtils.isEmpty(userParentDetail)) {
                sysObjectCommsVo.setParentCommsUserName(userParentDetail.getUserInfo().getUserNickname());

            }
        }

        // 填充评论用户昵称和用户头像
        sysObjectCommsVo.setUserPhoto(userDetail.getUserAvatar().getPictureUrl());
        sysObjectCommsVo.setUserNickName(userDetail.getUserInfo().getUserNickname());

        return sysObjectCommsVo;
    }

    /**
     * TODO 更新帖子评论数，redis
     * @param circlePostId 帖子id
     * @param isIncrement  是否是增加，true为增加，false为减少
     */
    private void updatePostCommentNum(Long circlePostId,Boolean isIncrement ) {

        // TODO 通过帖子id查询对应的帖子分类id，redis

        // TODO 通过帖子分类id查询对应的流量池，redis

        // TODO 通过帖子id在对应的流量池获取到帖子对象并添加评论数，修改流量池中该帖子的基本信息，redis
    }

    /**
     * 删除评论
     * @param objectCommsId 评论ID
     * @param circlePostId 帖子id
     */
    @Override
    public void deleteSysObjectComms(Long objectCommsId, Long circlePostId, Long userId) {
        // 删除评论及其子评论
        baseMapper.deleteById(objectCommsId);

        // 查询子评论
        List<SocialCirclePostComms> subSysObjectCommsList = baseMapper.selectList((new QueryWrapper<SocialCirclePostComms>()
                .eq("parent_comms_id", objectCommsId)));

        // 递归删除子评论
        if (!CollectionUtils.isEmpty(subSysObjectCommsList)) {
            for (SocialCirclePostComms subSysObjectComms : subSysObjectCommsList) {
                deleteSysObjectComms(subSysObjectComms.getObjectCommsId(), circlePostId, userId);
            }
        }

        // 更新帖子评论数
        this.updatePostCommentNum(circlePostId, false);
    }

    /**
     * 查询一级帖子评论，未分页
     * @param circlePostId 帖子ID
     * @return 评论对象
     */
    @Override
    public List<SysObjectCommsVo> selectSysObjectComms(Long circlePostId,Long currentPage,Long limit) {
        // 查询视频一级评论
        List<SocialCirclePostComms> primaryComments = baseMapper.selectList(
                new QueryWrapper<SocialCirclePostComms>()
                        .eq("commsed_object_id", circlePostId)
                        .eq("top_comms_id", 0)
                        .orderByDesc("created_time")
        );

        // 分页处理
        List<SocialCirclePostComms> pagedList = PageHelper.getPagedList(primaryComments, currentPage, limit);

        if (CollectionUtils.isEmpty(primaryComments)) {
            return Collections.emptyList();
        }

        return pagedList.stream()
                .map(this::buildCommentVo)
                .collect(Collectors.toList());
    }

    /**
     * 构建评论VO对象
     */
    private SysObjectCommsVo buildCommentVo(SocialCirclePostComms comment) {

        SysObjectCommsVo commentVo = BeanUtil.copyProperties(comment, SysObjectCommsVo::new);

        // 填充用户信息
        fillUserInfo(commentVo, comment.getUserId());

        // 填充子评论列表
        Boolean isHasChildren = buildSubComments(commentVo.getObjectCommsId());

        commentVo.setHasChildren(isHasChildren);
        return commentVo;
    }

    /**
     * 构建子评论列表
     */
    private Boolean buildSubComments(Long topCommentId) {
        return baseMapper.exists(
                new QueryWrapper<SocialCirclePostComms>()
                        .eq("top_comms_id", topCommentId)
                        .orderByDesc("created_time")
        );
    }

    /**
     * 填充评论用户信息
     */
    private void fillUserInfo(SysObjectCommsVo commentVo, Long userId) {
        UserDetail userDetail = getUserDetailFromCache(userId);
        if (userDetail != null) {
            commentVo.setUserNickName(userDetail.getUserInfo().getUserNickname());
            commentVo.setUserPhoto(userDetail.getUserAvatar().getPictureUrl());
        }
    }

    /**
     * 从缓存获取用户详情，增加空值检查
     */
    private UserDetail getUserDetailFromCache(Long userId) {
        if (userId == null) {
            return null;
        }
        try {
            return redisCache.getCacheObject(PermissKey.USER_DETAIL + userId);
        } catch (Exception e) {
            log.warn("获取用户详情失败，userId: " +  userId);
            return null;
        }
    }

    /**
     * 查询视频子评论
     * @param topCommsId 评论层次中的第一层次的评论id
     * @param currentPage 当前页
     * @param limit 每页显示条数
     * @return 评论对象
     */
    @Override
    public List<SysObjectCommsVo> selectSubSysObjectComms(Long topCommsId,Long currentPage,Long limit) {
        List<SocialCirclePostComms> subSysObjectCommsList = baseMapper.selectList((new QueryWrapper<SocialCirclePostComms>()
                .eq("top_comms_id", topCommsId))
                .orderByDesc("created_time"));

        // 分页处理
        List<SocialCirclePostComms> pagedList = PageHelper.getPagedList(subSysObjectCommsList, currentPage, limit);

        for (SocialCirclePostComms subSysObjectComms : pagedList) {

            UserDetail subUserDetail = redisCache
                    .getCacheObject(PermissKey.USER_DETAIL + subSysObjectComms.getParentCommsUserId());
            subSysObjectComms.setParentCommsUserName(subUserDetail.getUserInfo().getUserNickname());

            UserDetail subUserDetail2 = redisCache
                    .getCacheObject(PermissKey.USER_DETAIL + subSysObjectComms.getUserId());
            subSysObjectComms.setUserNickName(subUserDetail2.getUserInfo().getUserNickname());
            subSysObjectComms.setUserPhoto(subUserDetail2.getUserAvatar().getPictureUrl());

        }

        return BeanUtil.copyList(pagedList, SysObjectCommsVo.class);
    }
}
