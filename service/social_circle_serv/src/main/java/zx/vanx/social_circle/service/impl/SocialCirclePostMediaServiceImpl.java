package zx.vanx.social_circle.service.impl;

import zx.vanx.social_circle.entity.SocialCirclePostMedia;
import zx.vanx.social_circle.mapper.SocialCirclePostMediaMapper;
import zx.vanx.social_circle.service.SocialCirclePostMediaService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户的圈子帖子图片、视频表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Service
public class SocialCirclePostMediaServiceImpl extends ServiceImpl<SocialCirclePostMediaMapper, SocialCirclePostMedia> implements SocialCirclePostMediaService {

}
