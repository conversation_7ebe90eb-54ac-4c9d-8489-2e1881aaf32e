package zx.vanx.social_circle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import zx.vanx.common_util.core.page.PageObj;
import zx.vanx.common_util.core.redis.RedisCache;
import zx.vanx.common_util.exception.ZxException;
import zx.vanx.common_util.utils.PageHelper;
import zx.vanx.social_circle.entity.PlatfPostCateg;
import zx.vanx.social_circle.entity.SocialCircleMembers;
import zx.vanx.social_circle.entity.SocialCirclePostReport;
import zx.vanx.social_circle.mapper.SocialCirclePostReportMapper;
import zx.vanx.social_circle.request.PostReportPageRequest;
import zx.vanx.social_circle.request.PostReportRequest;
import zx.vanx.social_circle.service.PlatfPostCategService;
import zx.vanx.social_circle.service.SocialCircleMembersService;
import zx.vanx.social_circle.service.SocialCirclePostReportService;
import zx.vanx.social_circle.vo.PostReportManagementVO;
import zx.vanx.user_permiss.redis.key.PermissKey;
import zx.vanx.user_permiss.vo.UserDetail;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * <p>
 * 帖子举报信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SocialCirclePostReportServiceImpl extends ServiceImpl<SocialCirclePostReportMapper, SocialCirclePostReport> implements SocialCirclePostReportService {

    private final RedisCache redisCache;
    private final PlatfPostCategService platfPostCategService;
    private final SocialCircleMembersService socialCircleMembersService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SocialCirclePostReport reportPost(PostReportRequest request, Long reportUserId) {
        
        // 1. 参数验证
        if (request == null || reportUserId == null) {
            throw new ZxException(400, "举报参数不能为空");
        }

        // 2. 检查是否已经举报过
        if (hasUserReported(request.getCirclePostId(), reportUserId)) {
            throw new ZxException(400, "您已经举报过该帖子，请勿重复举报");
        }

        // 3. 创建举报记录
        SocialCirclePostReport report = new SocialCirclePostReport();
        // MyBatis-Plus会自动生成ID，不需要手动设置
        report.setSocialCircleId(request.getSocialCircleId());
        report.setCirclePostId(request.getCirclePostId());
        report.setReportUserId(reportUserId);
        report.setReportReason(request.getReportReason());
        report.setReportDescription(request.getReportDescription());
        report.setReportProcessingStatus("待处理"); // 默认状态为待处理

        // 4. 保存到数据库
        boolean saveResult = this.save(report);
        if (!saveResult) {
            throw new ZxException(500, "举报提交失败，请稍后重试");
        }

        log.info("用户[{}]举报帖子[{}]成功，举报原因：{}", reportUserId, request.getCirclePostId(), request.getReportReason());
        
        return report;
    }

    @Override
    public boolean hasUserReported(Long circlePostId, Long reportUserId) {
        if (circlePostId == null || reportUserId == null) {
            return false;
        }

        LambdaQueryWrapper<SocialCirclePostReport> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCirclePostReport::getCirclePostId, circlePostId)
                   .eq(SocialCirclePostReport::getReportUserId, reportUserId);

        return this.count(queryWrapper) > 0;
    }

    @Override
    public PageObj<PostReportManagementVO> getReportManagementList(PostReportPageRequest request, Long currentUserId) {
        
        // 1. 参数验证
        if (request == null || request.getPage() == null || request.getLimit() == null) {
            throw new ZxException(400, "分页参数不能为空");
        }
        if (currentUserId == null) {
            throw new ZxException(400, "当前用户ID不能为空");
        }
        if (request.getSocialCircleId() == null) {
            throw new ZxException(400, "圈子ID不能为空");
        }

        // 2. 验证当前用户是否为指定圈子的圈主
        validateOwnerPermission(request.getSocialCircleId(), currentUserId);

        // 3. 创建分页对象
        Page<PostReportManagementVO> page = new Page<>(request.getPage(), request.getLimit());

        // 4. 执行分页查询
        Page<PostReportManagementVO> resultPage = (Page<PostReportManagementVO>) baseMapper.selectReportManagementList(page, request);

        // 5. 处理查询结果，补充用户详情和分类信息
        if (resultPage.getRecords() != null && !resultPage.getRecords().isEmpty()) {
            for (PostReportManagementVO vo : resultPage.getRecords()) {
                // 补充用户头像和昵称信息
                enrichUserInfo(vo);
                // 补充分类名称信息
                enrichCategoryInfo(vo);
            }
        }

        // 6. 转换为PageObj并返回
        return PageHelper.convertToPageObj(resultPage);
    }

    /**
     * 补充用户详情信息（头像和昵称）
     * 从Redis缓存中获取用户详情
     * 
     * @param vo 举报管理VO对象
     */
    private void enrichUserInfo(PostReportManagementVO vo) {
        if (vo.getUserId() == null) {
            return;
        }

        try {
            // 从Redis获取用户详情
            UserDetail userDetail = redisCache.getCacheObject(PermissKey.USER_DETAIL + vo.getUserId());
            if (userDetail != null) {
                // 设置用户昵称
                if (userDetail.getUserInfo() != null) {
                    vo.setUserNickName(userDetail.getUserInfo().getUserNickname());
                }
                // 设置用户头像
                if (userDetail.getUserAvatar() != null) {
                    vo.setUserAvatar(userDetail.getUserAvatar().getPictureUrl());
                }
            }
        } catch (Exception e) {
            log.warn("获取用户详情失败，用户ID：{}，错误：{}", vo.getUserId(), e.getMessage());
        }
    }

    /**
     * 补充帖子分类名称信息
     * 通过分类ID获取分类名称
     * 
     * @param vo 举报管理VO对象
     */
    private void enrichCategoryInfo(PostReportManagementVO vo) {
        if (vo.getPostCategId() == null) {
            return;
        }

        try {
            // 获取帖子分类信息
            PlatfPostCateg categEntity = platfPostCategService.getById(vo.getPostCategId());
            if (categEntity != null && StringUtils.hasText(categEntity.getCategName())) {
                vo.setCategName(categEntity.getCategName());
            }
        } catch (Exception e) {
            log.warn("获取帖子分类信息失败，分类ID：{}，错误：{}", vo.getPostCategId(), e.getMessage());
        }
    }

    /**
     * 验证用户是否为圈主
     * 
     * @param socialCircleId 圈子ID
     * @param userId 用户ID
     */
    private void validateOwnerPermission(Long socialCircleId, Long userId) {
        LambdaQueryWrapper<SocialCircleMembers> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCircleMembers::getSocialCircleId, socialCircleId)
                .eq(SocialCircleMembers::getUserId, userId)
                .eq(SocialCircleMembers::getUserRole, "圈主")
                .eq(SocialCircleMembers::getUserStatus, "已入圈");

        SocialCircleMembers ownerRecord = socialCircleMembersService.getOne(queryWrapper);
        if (ownerRecord == null) {
            throw new ZxException(403, "只有圈主才能查看举报管理列表");
        }
    }

}
