package zx.vanx.social_circle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import zx.vanx.social_circle.entity.SocialCirclePostUsersRelat;
import zx.vanx.social_circle.mapper.SocialCirclePostUsersRelatMapper;
import zx.vanx.social_circle.service.SocialCirclePostUsersRelatService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * vanx_media_others_relat 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Service
public class SocialCirclePostUsersRelatServiceImpl extends ServiceImpl<SocialCirclePostUsersRelatMapper, SocialCirclePostUsersRelat> implements SocialCirclePostUsersRelatService {

    @Override
    public SocialCirclePostUsersRelat getByPostIdAndUserId(Long postId, Long userId) {
        if (postId == null || userId == null) {
            return null;
        }
        
        LambdaQueryWrapper<SocialCirclePostUsersRelat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCirclePostUsersRelat::getPostId, postId)
                   .eq(SocialCirclePostUsersRelat::getFromUserId, userId)
                   .last("LIMIT 1");
        
        return this.getOne(queryWrapper, false);
    }

}
