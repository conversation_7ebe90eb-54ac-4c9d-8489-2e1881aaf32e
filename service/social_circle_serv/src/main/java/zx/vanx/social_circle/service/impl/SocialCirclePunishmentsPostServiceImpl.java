package zx.vanx.social_circle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import zx.vanx.common_util.utils.BeanUtil;
import zx.vanx.social_circle.entity.SocialCirclePunishmentsPost;
import zx.vanx.social_circle.mapper.SocialCirclePunishmentsPostMapper;
import zx.vanx.social_circle.request.SocialCirclePunishmentsAddRequest;
import zx.vanx.social_circle.service.SocialCirclePunishmentsPostService;
import zx.vanx.social_circle.vo.SocialCirclePunishmentsPostVO;

import java.util.List;

/**
 * <p>
 * 圈子处罚记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Service
public class SocialCirclePunishmentsPostServiceImpl extends ServiceImpl<SocialCirclePunishmentsPostMapper, SocialCirclePunishmentsPost> implements SocialCirclePunishmentsPostService {

    /**
     * 新增用户处罚
     *
     * @param userId 进行处罚的用户id
     * @param publishRequest 处罚请求对象
     * @return 处罚记录的视图对象
     */
    @Override
    public SocialCirclePunishmentsPostVO addUserPunishments(Long userId, SocialCirclePunishmentsAddRequest publishRequest) {

        // 校验请求参数
        if (publishRequest == null
                || publishRequest.getSocialCircleId() == null
                || publishRequest.getPunishmentType() == null
                || publishRequest.getPunishmentUserId() == null) {
            throw new IllegalArgumentException("请求参数不完整");
        }

        // 查询这个时间点在是否有当前类型的处罚正在进行中，处罚的开始时间--处罚的结束时间，
        // 若是有且当前前端传入的时间段在改时间段内，那就不做修改，若是结束时间大于当前时间段那就修改结束时间，处罚延长TODO
        // 这里可以添加逻辑来检查是否有正在进行中的处罚记录
         List<SocialCirclePunishmentsPost> ongoingPunishments = this.list(new LambdaQueryWrapper<SocialCirclePunishmentsPost>()
                 .eq(SocialCirclePunishmentsPost::getSocialCircleId, publishRequest.getSocialCircleId())
                 .eq(SocialCirclePunishmentsPost::getPunishmentType, publishRequest.getPunishmentType())
                 .eq(SocialCirclePunishmentsPost::getPunishmentUserId, publishRequest.getPunishmentUserId())
                 .eq(SocialCirclePunishmentsPost::getPunishmentStatus, "进行中")
                 .ge(SocialCirclePunishmentsPost::getPunishmentStartDate, publishRequest.getPunishmentStartDate())
                 .le(SocialCirclePunishmentsPost::getPunishmentEndDate, publishRequest.getPunishmentEndDate()));

         if (!ongoingPunishments.isEmpty()) {
             throw new RuntimeException("当前用户在该圈子中已经有进行中的处罚记录，请勿重复添加");
         }

        // 创建处罚记录实体
        SocialCirclePunishmentsPost socialCirclePunishmentsPost = BeanUtil.copyProperties(publishRequest, SocialCirclePunishmentsPost::new);
        socialCirclePunishmentsPost.setPunishmentStatus("进行中"); // 设置处罚状态为进行中
        socialCirclePunishmentsPost.setCheckUserId(userId); // 设置执行处罚的用户ID

        // 保存处罚记录
        if (this.save(socialCirclePunishmentsPost)) {
            // 将实体转换为视图对象并返回
            return BeanUtil.copyProperties(socialCirclePunishmentsPost, SocialCirclePunishmentsPostVO::new);
        } else {
            // 如果保存失败，则抛出异常或返回null
            throw new RuntimeException("处罚添加失败，请稍后重试");
        }
    }
}
