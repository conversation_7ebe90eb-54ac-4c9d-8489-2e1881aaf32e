package zx.vanx.social_circle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import zx.vanx.common_util.core.redis.RedisCache;
import zx.vanx.common_util.exception.ZxException;
import zx.vanx.common_util.utils.BeanUtil;
import zx.vanx.social_circle.entity.SocialCircleInfo;
import zx.vanx.social_circle.entity.SocialCirclePunishmentsUser;
import zx.vanx.social_circle.mapper.SocialCirclePunishmentsUserMapper;
import zx.vanx.social_circle.service.SocialCircleInfoService;
import zx.vanx.social_circle.service.SocialCirclePunishmentsUserService;
import zx.vanx.social_circle.vo.SocialCirclePunishmentsUserVO;
import zx.vanx.social_circle.vo.UserPunishmentsListVO;
import zx.vanx.user_permiss.redis.key.PermissKey;
import zx.vanx.user_permiss.vo.UserDetail;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 圈子处罚记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SocialCirclePunishmentsUserServiceImpl extends ServiceImpl<SocialCirclePunishmentsUserMapper, SocialCirclePunishmentsUser> implements SocialCirclePunishmentsUserService {

    private final RedisCache redisCache;
    private final SocialCircleInfoService socialCircleInfoService;

    @Override
    public UserPunishmentsListVO getUserPunishmentsList(Long userId) {

        // 1. 参数验证
        if (userId == null) {
            throw new ZxException(400, "用户ID不能为空");
        }

        // 2. 查询用户的有效处罚记录
        LambdaQueryWrapper<SocialCirclePunishmentsUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SocialCirclePunishmentsUser::getPunishmentUserId, userId)
                   .eq(SocialCirclePunishmentsUser::getPunishmentStatus, "有效")
                   .orderByDesc(SocialCirclePunishmentsUser::getCreatedTime);

        List<SocialCirclePunishmentsUser> punishmentEntities = this.list(queryWrapper);

        // 3. 构建返回对象
        UserPunishmentsListVO result = new UserPunishmentsListVO();
        result.setUserId(userId);

        // 4. 从Redis获取用户基本信息
        enrichUserInfo(result, userId);

        // 5. 转换处罚记录并补充圈子信息
        List<SocialCirclePunishmentsUserVO> punishmentsVOList = punishmentEntities.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        // 6. 补充圈子名称信息
        punishmentsVOList.forEach(this::enrichCircleInfo);

        // 7. 设置结果数据
        result.setHasActivePunishment(!punishmentsVOList.isEmpty());
        result.setPunishmentsList(punishmentsVOList);

        return result;
    }

    /**
     * 从Redis获取用户基本信息并设置到结果对象中
     *
     * @param result 结果对象
     * @param userId 用户ID
     */
    private void enrichUserInfo(UserPunishmentsListVO result, Long userId) {
        try {
            // 从Redis获取用户详情
            UserDetail userDetail = redisCache.getCacheObject(PermissKey.USER_DETAIL + userId);
            if (userDetail != null) {
                // 设置用户昵称
                if (userDetail.getUserInfo() != null) {
                    result.setUserNickName(userDetail.getUserInfo().getUserNickname());
                }
                // 设置用户头像
                if (userDetail.getUserAvatar() != null) {
                    result.setUserAvatar(userDetail.getUserAvatar().getPictureUrl());
                }
            }
        } catch (Exception e) {
            log.warn("获取用户详情失败，用户ID：{}，错误：{}", userId, e.getMessage());
        }
    }

    /**
     * 将实体对象转换为VO对象
     *
     * @param entity 处罚实体对象
     * @return 处罚VO对象
     */
    private SocialCirclePunishmentsUserVO convertToVO(SocialCirclePunishmentsUser entity) {
        return BeanUtil.copyProperties(entity, SocialCirclePunishmentsUserVO::new);
    }

    /**
     * 补充圈子名称信息
     *
     * @param vo 处罚VO对象
     */
    private void enrichCircleInfo(SocialCirclePunishmentsUserVO vo) {
        if (vo.getSocialCircleId() == null) {
            return;
        }

        try {
            // 获取圈子信息
            SocialCircleInfo circleInfo = socialCircleInfoService.getById(vo.getSocialCircleId());
            if (circleInfo != null && StringUtils.hasText(circleInfo.getSocialCircleName())) {
                vo.setSocialCircleName(circleInfo.getSocialCircleName());
            }
        } catch (Exception e) {
            log.warn("获取圈子信息失败，圈子ID：{}，错误：{}", vo.getSocialCircleId(), e.getMessage());
        }
    }

}
