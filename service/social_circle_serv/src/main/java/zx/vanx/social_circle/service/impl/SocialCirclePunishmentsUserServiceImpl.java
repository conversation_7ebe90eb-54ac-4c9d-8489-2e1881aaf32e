package zx.vanx.social_circle.service.impl;

import zx.vanx.social_circle.entity.SocialCirclePunishmentsUser;
import zx.vanx.social_circle.mapper.SocialCirclePunishmentsUserMapper;
import zx.vanx.social_circle.service.SocialCirclePunishmentsUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 圈子处罚记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Service
public class SocialCirclePunishmentsUserServiceImpl extends ServiceImpl<SocialCirclePunishmentsUserMapper, SocialCirclePunishmentsUser> implements SocialCirclePunishmentsUserService {

}
