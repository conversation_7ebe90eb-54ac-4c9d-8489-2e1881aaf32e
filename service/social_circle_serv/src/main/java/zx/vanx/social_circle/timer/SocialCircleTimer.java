package zx.vanx.social_circle.timer;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import zx.vanx.common_util.core.redis.RedisCache;
import zx.vanx.social_circle.entity.SocialCirclePostUsersRelat;
import zx.vanx.social_circle.mapper.SocialCirclePostUsersRelatMapper;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 * 社交圈子定时任务服务
 * 负责定时同步Redis中的用户行为数据到MySQL数据库
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class SocialCircleTimer {

    private final RedisCache redisCache;

    private final SocialCirclePostUsersRelatMapper postUsersRelatMapper;

    // Redis键前缀常量
    private static final String CIRCLE_POST_PREFIX = "circle:post:";
    private static final String BEHAVIOR_LIKES = "likes";
    private static final String BEHAVIOR_FORWARDS = "forwards";
    private static final String BEHAVIOR_COLLECTS = "collects";
    private static final String BEHAVIOR_COMMENTS = "comments";
    private static final String BEHAVIOR_GIFTS = "gifts";

    // 正则表达式用于解析Redis键
    private static final Pattern POST_BEHAVIOR_PATTERN = Pattern.compile("circle:post:(\\d+):(\\w+)");

    /**
     * 定时同步Redis中的用户行为数据到MySQL数据库
     * 每30分钟执行一次，避免频繁数据库操作
     */
//    @Scheduled(cron = "0 0/30 * * * *") // 每30分钟执行一次
    @Scheduled(cron = "0/10 * * * * *")
    public void syncUserBehaviorDataToDatabase() {
        try {
            log.info("开始同步用户行为数据到数据库...");
            
            // 1. 扫描所有用户行为相关的Redis键
            List<String> behaviorKeys = scanAllBehaviorKeys();
            
            if (CollectionUtils.isEmpty(behaviorKeys)) {
                log.info("未发现需要同步的用户行为数据");
                return;
            }
            
            // 2. 处理每个行为键，获取用户行为数据
            Map<String, SocialCirclePostUsersRelat> userBehaviorMap = new HashMap<>();
            
            for (String key : behaviorKeys) {
                processUserBehaviorKey(key, userBehaviorMap);
            }
            
            if (userBehaviorMap.isEmpty()) {
                log.info("未发现有效的用户行为数据");
                return;
            }
            
            // 3. 批量同步数据到数据库
            int syncCount = syncBehaviorDataToDatabase(userBehaviorMap.values());
            
            log.info("用户行为数据同步完成，共同步: {} 条记录", syncCount);
            
        } catch (Exception e) {
            // 定时任务必须捕获异常，防止任务调度器停止后续执行
            log.error("用户行为数据同步失败", e);
        }
    }

    /**
     * 扫描所有用户行为相关的Redis键
     * 
     * @return 行为键列表
     */
    private List<String> scanAllBehaviorKeys() {
        List<String> allKeys = new ArrayList<>();
        
        // 扫描各种行为类型的键
        String[] behaviors = {BEHAVIOR_LIKES, BEHAVIOR_FORWARDS, BEHAVIOR_COLLECTS, BEHAVIOR_COMMENTS, BEHAVIOR_GIFTS};
        
        for (String behavior : behaviors) {
            String keyPattern = CIRCLE_POST_PREFIX + "*:" + behavior;
            Collection<String> keys = redisCache.keys(keyPattern);
            if (!CollectionUtils.isEmpty(keys)) {
                allKeys.addAll(keys);
            }
        }
        
        return allKeys;
    }

    /**
     * 处理单个用户行为键，解析数据并填充到用户行为映射中
     * 
     * @param key Redis键
     * @param userBehaviorMap 用户行为数据映射
     */
    private void processUserBehaviorKey(String key, Map<String, SocialCirclePostUsersRelat> userBehaviorMap) {
        try {
            // 解析Redis键获取帖子ID和行为类型
            Matcher matcher = POST_BEHAVIOR_PATTERN.matcher(key);
            if (!matcher.matches()) {
                return;
            }
            
            Long postId = Long.parseLong(matcher.group(1));
            String behaviorType = matcher.group(2);
            
            // 获取该键下的所有用户ID（Set成员）
            Set<String> userIds = redisCache.getCacheSet(key);
            
            if (CollectionUtils.isEmpty(userIds)) {
                return;
            }
            
            // 为每个用户创建或更新行为记录
            userIds.forEach(userIdStr -> processUserBehaviorRecord(userIdStr, postId, behaviorType, userBehaviorMap));
            
        } catch (Exception e) {
            log.warn("处理Redis键失败: {}", key, e);
        }
    }
    
    /**
     * 处理单个用户行为记录
     */
    private void processUserBehaviorRecord(String userIdStr, Long postId, String behaviorType, 
                                         Map<String, SocialCirclePostUsersRelat> userBehaviorMap) {
        try {
            Long userId = Long.parseLong(userIdStr);
            String mapKey = postId + "_" + userId;
            
            SocialCirclePostUsersRelat relation = userBehaviorMap.computeIfAbsent(mapKey, k -> {
                SocialCirclePostUsersRelat newRelation = new SocialCirclePostUsersRelat();
                newRelation.setPostId(postId);
                newRelation.setFromUserId(userId);
                return newRelation;
            });
            
            // 根据行为类型设置对应字段
            setBehaviorFlag(relation, behaviorType, true);
            
        } catch (NumberFormatException e) {
            log.warn("解析用户ID失败: {}", userIdStr);
        }
    }

    /**
     * 根据行为类型设置对应的布尔标志
     * 
     * @param relation 用户帖子关系实体
     * @param behaviorType 行为类型
     * @param value 设置值
     */
    private void setBehaviorFlag(SocialCirclePostUsersRelat relation, String behaviorType, boolean value) {
        switch (behaviorType) {
            case BEHAVIOR_LIKES:
                relation.setIsLike(value);
                break;
            case BEHAVIOR_FORWARDS:
                relation.setIsForwarding(value);
                break;
            case BEHAVIOR_COLLECTS:
                relation.setIsCollect(value);
                break;
            case BEHAVIOR_COMMENTS:
                relation.setIsComment(value);
                break;
            case BEHAVIOR_GIFTS:
                relation.setIsGift(value);
                break;
            default:
                log.warn("未知的行为类型: {}", behaviorType);
        }
    }

    /**
     * 批量同步行为数据到数据库
     * 采用insert or update策略
     * 
     * @param behaviorData 行为数据集合
     * @return 同步的记录数
     */
    private int syncBehaviorDataToDatabase(Collection<SocialCirclePostUsersRelat> behaviorData) {
        return behaviorData.stream()
                .mapToInt(this::syncSingleBehaviorRecord)
                .sum();
    }
    
    /**
     * 同步单个用户行为记录
     */
    private int syncSingleBehaviorRecord(SocialCirclePostUsersRelat relation) {
        try {
            // 查询数据库中是否已存在该用户对该帖子的行为记录
            LambdaQueryWrapper<SocialCirclePostUsersRelat> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SocialCirclePostUsersRelat::getPostId, relation.getPostId())
                       .eq(SocialCirclePostUsersRelat::getFromUserId, relation.getFromUserId());
            
            SocialCirclePostUsersRelat existingRelation = postUsersRelatMapper.selectOne(queryWrapper);
            
            if (existingRelation != null) {
                // 更新现有记录
                updateExistingRelation(existingRelation, relation);
                return postUsersRelatMapper.updateById(existingRelation);
            } else {
                // 插入新记录
                return postUsersRelatMapper.insert(relation);
            }
            
        } catch (Exception e) {
            log.warn("同步用户行为数据失败, postId: {}, userId: {}", 
                    relation.getPostId(), relation.getFromUserId(), e);
            return 0;
        }
    }

    /**
     * 更新现有的用户行为记录
     * 合并Redis中的行为数据到现有记录
     * 
     * @param existing 现有记录
     * @param newData 新数据
     */
    private void updateExistingRelation(SocialCirclePostUsersRelat existing, SocialCirclePostUsersRelat newData) {
        // 更新各种行为标志（只有Redis中为true时才更新，避免覆盖已有的true值）
        if (Boolean.TRUE.equals(newData.getIsLike())) {
            existing.setIsLike(true);
        }
        if (Boolean.TRUE.equals(newData.getIsForwarding())) {
            existing.setIsForwarding(true);
        }
        if (Boolean.TRUE.equals(newData.getIsCollect())) {
            existing.setIsCollect(true);
        }
        if (Boolean.TRUE.equals(newData.getIsComment())) {
            existing.setIsComment(true);
        }
        if (Boolean.TRUE.equals(newData.getIsGift())) {
            existing.setIsGift(true);
        }
        
        // 更新修改时间
        existing.setModifiedTime(LocalDateTime.now());
    }

}
