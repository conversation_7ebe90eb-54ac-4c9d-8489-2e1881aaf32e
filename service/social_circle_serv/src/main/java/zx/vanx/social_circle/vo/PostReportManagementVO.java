package zx.vanx.social_circle.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 管理员举报管理视图对象
 * 用于展示被举报帖子的详细信息和举报统计数据
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@ApiModel(value = "PostReportManagementVO对象", description = "管理员举报管理视图对象")
public class PostReportManagementVO {

    @ApiModelProperty(value = "帖子ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long circlePostId;

    @ApiModelProperty(value = "社交圈子ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long socialCircleId;

    @ApiModelProperty(value = "圈子名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String socialCircleName;

    @ApiModelProperty(value = "帖子状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String postStatus;

    @ApiModelProperty(value = "发帖用户头像")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String userAvatar;

    @ApiModelProperty(value = "发帖用户昵称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String userNickName;

    @ApiModelProperty(value = "发帖用户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long userId;

    @ApiModelProperty(value = "帖子发布时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private LocalDateTime postCreatedTime;

    @ApiModelProperty(value = "帖子标题")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String circlePostTitle;

    @ApiModelProperty(value = "帖子内容")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String circlePostContent;

    @ApiModelProperty(value = "帖子封面URL")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String postCoverUrl;

    @ApiModelProperty(value = "帖子分类ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long postCategId;

    @ApiModelProperty(value = "帖子分类名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String categName;

    @ApiModelProperty(value = "举报数量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long reportCount;

    @ApiModelProperty(value = "首次举报时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private LocalDateTime firstReportTime;

    @ApiModelProperty(value = "最近举报时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private LocalDateTime latestReportTime;

    @ApiModelProperty(value = "已处理举报数量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long processedReportCount;

    @ApiModelProperty(value = "待处理举报数量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long pendingReportCount;

    @ApiModelProperty(value = "帖子位置信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String location;

    @ApiModelProperty(value = "省份")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String provinceName;

    @ApiModelProperty(value = "城市")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String cityName;

    @ApiModelProperty(value = "分享类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String sharedType;

}