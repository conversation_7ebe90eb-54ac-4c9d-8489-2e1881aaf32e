package zx.vanx.social_circle.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class SocialCirclePunishmentsPostVO {

    @ApiModelProperty(value = "id")
    @TableId(value = "circle_punishments_id", type = IdType.NONE)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long circlePunishmentsId;

    @ApiModelProperty(value = "圈子id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long socialCircleId;

    @ApiModelProperty(value = "被处罚的用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long punishmentUserId;

    @ApiModelProperty(value = "处罚的类型：警告、禁言、封号、限制权限等")
    private String punishmentType;

    @ApiModelProperty(value = "处罚的开始时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date punishmentStartDate;

    @ApiModelProperty(value = "处罚的结束时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date punishmentEndDate;

    @ApiModelProperty(value = "处罚持续时间：禁言24小时")
    private String punishmentDuration;

    @ApiModelProperty(value = "处罚状态：进行中、已完成")
    private String punishmentStatus;

    @ApiModelProperty(value = "进行处罚的管理员或用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long checkUserId;
}
