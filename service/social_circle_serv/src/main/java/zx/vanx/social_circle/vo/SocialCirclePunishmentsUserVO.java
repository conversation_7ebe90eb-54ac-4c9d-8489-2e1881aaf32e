package zx.vanx.social_circle.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 用户处罚记录视图对象
 * 用于展示用户的处罚记录详细信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@ApiModel(value = "SocialCirclePunishmentsUserVO对象", description = "用户处罚记录视图对象")
public class SocialCirclePunishmentsUserVO {

    @ApiModelProperty(value = "处罚记录ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long circlePunishmentsId;

    @ApiModelProperty(value = "圈子ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long socialCircleId;

    @ApiModelProperty(value = "圈子名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String socialCircleName;

    @ApiModelProperty(value = "被处罚的用户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long punishmentUserId;

    @ApiModelProperty(value = "处罚类型：警告、禁言、封号、限制权限等")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String punishmentType;

    @ApiModelProperty(value = "处罚开始时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date punishmentStartDate;

    @ApiModelProperty(value = "处罚结束时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date punishmentEndDate;

    @ApiModelProperty(value = "处罚持续时间：禁言24小时")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String punishmentDuration;

    @ApiModelProperty(value = "处罚原因")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String punishmentReason;

    @ApiModelProperty(value = "处罚状态：有效、已解除")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String punishmentStatus;

    @ApiModelProperty(value = "用户处罚申诉状态：申诉处理中、申诉通过、申诉被驳回")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String userAppealStatus;

    @ApiModelProperty(value = "申诉理由")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String reasonForAppeal;

    @ApiModelProperty(value = "进行处罚的管理员或用户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long checkUserId;

    @ApiModelProperty(value = "处罚记录创建时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date createdTime;

}
