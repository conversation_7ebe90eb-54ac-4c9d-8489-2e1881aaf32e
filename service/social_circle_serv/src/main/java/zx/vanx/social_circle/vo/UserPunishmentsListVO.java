package zx.vanx.social_circle.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 用户处罚列表视图对象
 * 包含用户基本信息和处罚记录列表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@ApiModel(value = "UserPunishmentsListVO对象", description = "用户处罚列表视图对象")
public class UserPunishmentsListVO {

    @ApiModelProperty(value = "用户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long userId;

    @ApiModelProperty(value = "用户昵称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String userNickName;

    @ApiModelProperty(value = "用户头像URL")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String userAvatar;

//    @ApiModelProperty(value = "处罚记录总数")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Integer totalPunishments;
//
//    @ApiModelProperty(value = "有效处罚数量")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Integer activePunishments;
//
//    @ApiModelProperty(value = "已解除处罚数量")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Integer resolvedPunishments;

    @ApiModelProperty(value = "处罚记录列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<SocialCirclePunishmentsUserVO> punishmentsList;

}
