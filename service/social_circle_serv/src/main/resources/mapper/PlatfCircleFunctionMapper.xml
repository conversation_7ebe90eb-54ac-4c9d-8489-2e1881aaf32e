<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.social_circle.mapper.PlatfCircleFunctionMapper">

    <!-- 共享的字段列表SQL片段 -->
    <sql id="functionColumns">
        function_id,
        function_title,
        parent_function_id,
        associate_function_id,
        sort_number,
        path,
        component,
        function_type,
        perms,
        icon,
        storage_size,
        is_charged,
        is_frame,
        is_visible,
        status,
        is_default,
        function_categ,
        function_location,
        prompt_message,
        icon_url,
        request_url,
        target_type,
        function_status,
        is_leaf,
        module_name,
        operation_name
    </sql>

    <select id="selectCircleDetailMenus" resultType="zx.vanx.social_circle.entity.PlatfCircleFunction">
        SELECT 
            <include refid="functionColumns"/>
        FROM vanx_platf_circle_function
        WHERE is_deleted = 0
        AND (function_title = #{functionTitle} 
             OR parent_function_id IN (
                 SELECT function_id 
                 FROM vanx_platf_circle_function 
                 WHERE function_title = #{functionTitle} AND is_deleted = 0
             ))
        ORDER BY parent_function_id, sort_number
    </select>

    <select id="selectCircleManagementMenus" resultType="zx.vanx.social_circle.entity.PlatfCircleFunction">
        SELECT
            <include refid="functionColumns"/>
        FROM vanx_platf_circle_function
        WHERE is_deleted = 0
        AND (function_title = #{functionTitle}
             OR parent_function_id IN (
                 SELECT function_id
                 FROM vanx_platf_circle_function
                 WHERE function_title = #{functionTitle} AND is_deleted = 0
             ))
        ORDER BY parent_function_id, sort_number
    </select>

    <select id="selectAssociatedFunctions" resultType="zx.vanx.social_circle.entity.PlatfCircleFunction">
        SELECT 
            <include refid="functionColumns"/>
        FROM vanx_platf_circle_function
        WHERE is_deleted = 0
        AND function_id IN (
            SELECT associate_function_id 
            FROM vanx_platf_circle_function 
            WHERE function_id IN 
            <foreach collection="functionIds" item="functionId" open="(" close=")" separator=",">
                #{functionId}
            </foreach>
            AND associate_function_id IS NOT NULL
            AND is_deleted = 0
        )
    </select>

    <select id="selectDefaultFunctions" resultType="zx.vanx.social_circle.entity.PlatfCircleFunction">
        SELECT 
            <include refid="functionColumns"/>
        FROM vanx_platf_circle_function
        WHERE is_deleted = 0
        AND is_default = 1
        AND parent_function_id IN (
            SELECT function_id 
            FROM vanx_platf_circle_function 
            WHERE function_title = #{parentFunctionTitle} AND is_deleted = 0
        )
        ORDER BY sort_number
    </select>

    <!-- 递归查询指定菜单下的所有子菜单（排除指定菜单本身） -->
    <select id="selectAllSubMenusRecursive" resultType="zx.vanx.social_circle.entity.PlatfCircleFunction">
        WITH RECURSIVE menu_tree AS (
            -- 基础查询：找到指定菜单的直接子菜单
            SELECT 
                <include refid="functionColumns"/>
            FROM vanx_platf_circle_function 
            WHERE is_deleted = 0
            AND parent_function_id IN (
                SELECT function_id 
                FROM vanx_platf_circle_function 
                WHERE function_title = #{parentFunctionTitle} AND is_deleted = 0
            )
            
            UNION ALL
            
            -- 递归查询：查找子菜单的子菜单
            SELECT 
                pcf.function_id,
                pcf.function_title,
                pcf.parent_function_id,
                pcf.associate_function_id,
                pcf.sort_number,
                pcf.path,
                pcf.component,
                pcf.function_type,
                pcf.perms,
                pcf.icon,
                pcf.storage_size,
                pcf.is_charged,
                pcf.is_frame,
                pcf.is_visible,
                pcf.status,
                pcf.is_default,
                pcf.function_categ,
                pcf.function_location,
                pcf.prompt_message,
                pcf.icon_url,
                pcf.request_url,
                pcf.target_type,
                pcf.function_status,
                pcf.is_leaf,
                pcf.module_name,
                pcf.operation_name
            FROM vanx_platf_circle_function pcf
            INNER JOIN menu_tree mt ON pcf.parent_function_id = mt.function_id
            WHERE pcf.is_deleted = 0
        )
        SELECT DISTINCT * FROM menu_tree
        ORDER BY parent_function_id, sort_number
    </select>

    <!-- 递归查询指定菜单ID列表下所有function_type为F（按钮）的子菜单 -->
    <select id="selectButtonSubMenusRecursive" resultType="zx.vanx.social_circle.entity.PlatfCircleFunction">
        WITH RECURSIVE button_menu_tree AS (
            -- 基础查询：找到指定菜单ID列表的直接子菜单中function_type为F的
            SELECT 
                <include refid="functionColumns"/>
            FROM vanx_platf_circle_function 
            WHERE is_deleted = 0
            AND function_type = 'F'
            AND parent_function_id IN 
            <foreach collection="functionIds" item="functionId" open="(" close=")" separator=",">
                #{functionId}
            </foreach>
            
            UNION ALL
            
            -- 递归查询：查找子菜单的子菜单中function_type为F的
            SELECT 
                pcf.function_id,
                pcf.function_title,
                pcf.parent_function_id,
                pcf.associate_function_id,
                pcf.sort_number,
                pcf.path,
                pcf.component,
                pcf.function_type,
                pcf.perms,
                pcf.icon,
                pcf.storage_size,
                pcf.is_charged,
                pcf.is_frame,
                pcf.is_visible,
                pcf.status,
                pcf.is_default,
                pcf.function_categ,
                pcf.function_location,
                pcf.prompt_message,
                pcf.icon_url,
                pcf.request_url,
                pcf.target_type,
                pcf.function_status,
                pcf.is_leaf,
                pcf.module_name,
                pcf.operation_name
            FROM vanx_platf_circle_function pcf
            INNER JOIN button_menu_tree bmt ON pcf.parent_function_id = bmt.function_id
            WHERE pcf.is_deleted = 0
            AND pcf.function_type = 'F'
        )
        SELECT DISTINCT * FROM button_menu_tree
        ORDER BY parent_function_id, sort_number
    </select>

</mapper>
