<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.social_circle.mapper.SocialCircleFuncRelatMapper">

    <!-- 共享的字段列表SQL片段（使用表别名pcf） -->
    <sql id="functionColumnsWithAlias">
        pcf.function_id,
        pcf.function_title,
        pcf.parent_function_id,
        pcf.associate_function_id,
        pcf.sort_number,
        pcf.path,
        pcf.component,
        pcf.function_type,
        pcf.perms,
        pcf.icon,
        pcf.storage_size,
        pcf.is_charged,
        pcf.is_frame,
        pcf.is_visible,
        pcf.status,
        pcf.is_default,
        pcf.function_categ,
        pcf.function_location,
        pcf.prompt_message,
        pcf.icon_url,
        pcf.request_url,
        pcf.target_type,
        pcf.function_status,
        pcf.is_leaf,
        pcf.module_name,
        pcf.operation_name
    </sql>

    <!--查询指定圈子中指定角色的功能菜单列表-->
    <select id="selectUserFunctionMenus" resultType="zx.vanx.social_circle.entity.PlatfCircleFunction">
        SELECT 
            <include refid="functionColumnsWithAlias"/>
        FROM vanx_social_circle_func_relat scfr
        INNER JOIN vanx_platf_circle_function pcf ON scfr.function_id = pcf.function_id
        WHERE scfr.social_circle_id = #{socialCircleId}
        AND scfr.role_name = #{roleName}
        AND scfr.is_deleted = 0
        AND pcf.is_deleted = 0
        ORDER BY pcf.parent_function_id, pcf.sort_number
    </select>

</mapper>
