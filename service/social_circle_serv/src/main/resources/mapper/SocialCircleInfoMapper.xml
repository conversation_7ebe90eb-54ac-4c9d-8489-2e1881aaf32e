<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.social_circle.mapper.SocialCircleInfoMapper">

    <!-- 条件搜索圈子列表（分页查询） -->
    <select id="searchCirclesWithPaging" parameterType="zx.vanx.social_circle.request.SocialCircleSearchRequest" 
            resultType="zx.vanx.social_circle.vo.SocialCircleSearchVO">
        SELECT 
            sci.social_circle_id,
            sci.social_circle_name,
            sci.social_circle_description,
            sci.social_circle_advertise,
            sci.social_circle_logo_url,
            sci.social_circle_status,
            sci.visibility_status,
            sci.user_id,
            sci.created_time AS createTime,
            sci.modified_time AS updateTime,
            
            -- 地址信息
            sca.province_name,
            sca.city_name,
            sca.district_name,
            sca.town_name,
            sca.full_address,
            
            -- 统计信息
            COALESCE(member_stats.member_count, 0) AS memberCount,
            COALESCE(member_stats.active_member_count, 0) AS activeMemberCount,
            COALESCE(media_stats.media_count, 0) AS mediaCount
            
        FROM vanx_social_circle_info sci
        
        -- 左连接地址表
        LEFT JOIN vanx_social_circle_address sca ON sci.social_circle_id = sca.social_circle_id 
            AND sca.is_deleted = 0
        
        -- 左连接成员统计
        LEFT JOIN (
            SELECT 
                social_circle_id,
                COUNT(*) AS member_count,
                COUNT(CASE WHEN DATE(modified_time) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 END) AS active_member_count
            FROM vanx_social_circle_members 
            WHERE is_deleted = 0 AND user_status = '已入圈'
            GROUP BY social_circle_id
        ) member_stats ON sci.social_circle_id = member_stats.social_circle_id
        
        -- 左连接媒体统计
        LEFT JOIN (
            SELECT 
                social_circle_id,
                COUNT(*) AS media_count
            FROM vanx_social_circle_media 
            WHERE is_deleted = 0
            GROUP BY social_circle_id
        ) media_stats ON sci.social_circle_id = media_stats.social_circle_id
        
        <where>
            <!-- 基础过滤条件：只查询审核通过且公开的圈子 -->
            sci.is_deleted = 0
            AND sci.social_circle_status = '开通'
            AND sci.visibility_status = '公开'
            
            <!-- 圈子名称模糊查询 -->
            <if test="request.socialCircleName != null and request.socialCircleName.trim() != ''">
                AND sci.social_circle_name LIKE CONCAT('%', #{request.socialCircleName}, '%')
            </if>
            
            <!-- 分类ID精确查询 -->
            <if test="request.categoryId != null">
                AND EXISTS (
                    SELECT 1 FROM vanx_social_circle_category_relat sccr 
                    WHERE sccr.social_circle_id = sci.social_circle_id 
                    AND sccr.social_circle_categ_id = #{request.categoryId}
                    AND sccr.is_deleted = 0
                )
            </if>
            
            <!-- 地址相关查询条件 -->
            <if test="request.provinceName != null and request.provinceName.trim() != ''">
                AND sca.province_name = #{request.provinceName}
            </if>
            
            <if test="request.cityName != null and request.cityName.trim() != ''">
                AND sca.city_name = #{request.cityName}
            </if>
            
            <if test="request.districtName != null and request.districtName.trim() != ''">
                AND sca.district_name = #{request.districtName}
            </if>
            
            <if test="request.townName != null and request.townName.trim() != ''">
                AND sca.town_name LIKE CONCAT('%', #{request.townName}, '%')
            </if>
            
            <if test="request.fullAddress != null and request.fullAddress.trim() != ''">
                AND sca.full_address LIKE CONCAT('%', #{request.fullAddress}, '%')
            </if>
        </where>
        
        <!-- 排序 -->
        <choose>
            <when test="request.orderBy == 'memberCount'">
                ORDER BY member_stats.member_count
                <if test="request.orderDirection == 'desc'">DESC</if>
                <if test="request.orderDirection == 'asc'">ASC</if>
            </when>
            <when test="request.orderBy == 'updateTime'">
                ORDER BY sci.modified_time
                <if test="request.orderDirection == 'desc'">DESC</if>
                <if test="request.orderDirection == 'asc'">ASC</if>
            </when>
            <otherwise>
                ORDER BY sci.created_time
                <if test="request.orderDirection == 'desc'">DESC</if>
                <if test="request.orderDirection == 'asc'">ASC</if>
            </otherwise>
        </choose>
        
        <!-- 分页 -->
        LIMIT #{request.limit} OFFSET ${(request.page - 1) * request.limit}
    </select>

    <!-- 统计条件搜索圈子的总数量 -->
    <select id="countSearchCircles" parameterType="zx.vanx.social_circle.request.SocialCircleSearchRequest" 
            resultType="java.lang.Long">
        SELECT COUNT(DISTINCT sci.social_circle_id)
        FROM vanx_social_circle_info sci
        
        -- 左连接地址表（只在有地址查询条件时才需要）
        <if test="request.provinceName != null and request.provinceName.trim() != '' 
                  or request.cityName != null and request.cityName.trim() != ''
                  or request.districtName != null and request.districtName.trim() != ''
                  or request.townName != null and request.townName.trim() != ''
                  or request.fullAddress != null and request.fullAddress.trim() != ''">
            LEFT JOIN vanx_social_circle_address sca ON sci.social_circle_id = sca.social_circle_id 
                AND sca.is_deleted = 0
        </if>
        
        <where>
            <!-- 基础过滤条件：只查询审核通过且公开的圈子 -->
            sci.is_deleted = 0
            AND sci.social_circle_status = '开通'
            AND sci.visibility_status = '公开'
            
            <!-- 圈子名称模糊查询 -->
            <if test="request.socialCircleName != null and request.socialCircleName.trim() != ''">
                AND sci.social_circle_name LIKE CONCAT('%', #{request.socialCircleName}, '%')
            </if>
            
            <!-- 分类ID精确查询 -->
            <if test="request.categoryId != null">
                AND EXISTS (
                    SELECT 1 FROM vanx_social_circle_category_relat sccr 
                    WHERE sccr.social_circle_id = sci.social_circle_id 
                    AND sccr.social_circle_categ_id = #{request.categoryId}
                    AND sccr.is_deleted = 0
                )
            </if>
            
            <!-- 地址相关查询条件 -->
            <if test="request.provinceName != null and request.provinceName.trim() != ''">
                AND sca.province_name = #{request.provinceName}
            </if>
            
            <if test="request.cityName != null and request.cityName.trim() != ''">
                AND sca.city_name = #{request.cityName}
            </if>
            
            <if test="request.districtName != null and request.districtName.trim() != ''">
                AND sca.district_name = #{request.districtName}
            </if>
            
            <if test="request.townName != null and request.townName.trim() != ''">
                AND sca.town_name LIKE CONCAT('%', #{request.townName}, '%')
            </if>
            
            <if test="request.fullAddress != null and request.fullAddress.trim() != ''">
                AND sca.full_address LIKE CONCAT('%', #{request.fullAddress}, '%')
            </if>
        </where>
    </select>

    <!-- 根据圈子ID查询圈子的分类信息列表 -->
    <select id="getCategoriesByCircleId" parameterType="java.lang.Long" 
            resultType="zx.vanx.social_circle.vo.SocialCircleSearchVO$CategoryInfo">
        SELECT 
            spc.social_circle_categ_id AS categoryId,
            spc.social_circle_categ_name AS categoryName,
            spc.social_circle_categ_level AS categoryLevel,
            spc.parent_social_circle_categ_id AS parentCategoryId
        FROM vanx_social_circle_category_relat sccr
        INNER JOIN vanx_social_circle_platf_category spc ON sccr.social_circle_categ_id = spc.social_circle_categ_id
        WHERE sccr.social_circle_id = #{circleId}
            AND sccr.is_deleted = 0
            AND spc.is_deleted = 0
            AND spc.category_status = '通过'
        ORDER BY spc.social_circle_categ_level ASC, sccr.sort_number ASC
    </select>

    <!-- 查询普通成员在指定圈子中可访问的按钮权限列表 -->
    <select id="selectMemberButtonPermissions" resultType="zx.vanx.social_circle.entity.PlatfCircleFunction">
        WITH RECURSIVE button_permissions AS (
            -- Step 1: 查询圈子详情下的C类型菜单（直接子菜单）
            SELECT pcf.function_id as c_menu_id
            FROM vanx_platf_circle_function pcf
            WHERE pcf.is_deleted = 0
            AND pcf.function_type = 'C'
            AND pcf.parent_function_id IN (
                SELECT function_id 
                FROM vanx_platf_circle_function 
                WHERE function_title = '圈子详情' AND is_deleted = 0
            )
            
            UNION ALL
            
            -- 递归查询：获取所有子级C菜单
            SELECT child.function_id as c_menu_id
            FROM vanx_platf_circle_function child
            INNER JOIN button_permissions bp ON child.parent_function_id = bp.c_menu_id
            WHERE child.is_deleted = 0
            AND child.function_type = 'C'
        )
        SELECT DISTINCT
            pcf.function_id,
            pcf.function_title,
            pcf.parent_function_id,
            pcf.associate_function_id,
            pcf.sort_number,
            pcf.path,
            pcf.component,
            pcf.function_type,
            pcf.perms,
            pcf.icon,
            pcf.storage_size,
            pcf.is_charged,
            pcf.is_frame,
            pcf.is_visible,
            pcf.status,
            pcf.is_default,
            pcf.function_categ,
            pcf.function_location,
            pcf.prompt_message,
            pcf.icon_url,
            pcf.request_url,
            pcf.target_type,
            pcf.function_status,
            pcf.is_leaf,
            pcf.module_name,
            pcf.operation_name
        FROM button_permissions bp
        -- Step 2: 查询按钮菜单
        INNER JOIN vanx_platf_circle_function pcf ON pcf.parent_function_id = bp.c_menu_id
        -- Step 3: 权限过滤
        INNER JOIN vanx_social_circle_func_relat scfr ON scfr.function_id = pcf.function_id
        WHERE pcf.is_deleted = 0
        AND pcf.function_type = 'F'
        AND scfr.is_deleted = 0
        AND scfr.social_circle_id = #{socialCircleId}
        AND scfr.role_name = '普通成员'
        ORDER BY pcf.parent_function_id, pcf.sort_number
    </select>

</mapper>
