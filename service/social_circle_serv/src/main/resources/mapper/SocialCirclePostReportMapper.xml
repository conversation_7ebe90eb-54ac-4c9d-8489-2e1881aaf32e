<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.social_circle.mapper.SocialCirclePostReportMapper">

    <!-- 管理员分页查询举报清单 -->
    <select id="selectReportManagementList" resultType="zx.vanx.social_circle.vo.PostReportManagementVO">
        SELECT 
            p.circle_post_id AS circlePostId,
            p.social_circle_id AS socialCircleId,
            sci.social_circle_name AS socialCircleName,
            p.post_status AS postStatus,
            p.user_id AS userId,
            p.created_time AS postCreatedTime,
            p.circle_post_title AS circlePostTitle,
            p.circle_post_content AS circlePostContent,
            p.post_cover_url AS postCoverUrl,
            p.post_categ_id AS postCategId,
            p.location AS location,
            p.province_name AS provinceName,
            p.city_name AS cityName,
            p.shared_type AS sharedType,
            COUNT(r.post_report_id) AS reportCount,
            MIN(r.created_time) AS firstReportTime,
            MAX(r.created_time) AS latestReportTime,
            <!-- 获取最新的举报处理状态 -->
            SUBSTRING_INDEX(GROUP_CONCAT(r.report_processing_status ORDER BY r.created_time DESC), ',', 1) AS reportProcessingStatus
        FROM vanx_social_circle_post_report r
        LEFT JOIN vanx_social_circle_post p ON r.circle_post_id = p.circle_post_id
        LEFT JOIN vanx_social_circle_info sci ON p.social_circle_id = sci.social_circle_id
        WHERE 1=1
            <!-- 举报处理状态筛选 -->
            <if test="request.reportProcessingStatus != null and request.reportProcessingStatus != ''">
                AND r.report_processing_status = #{request.reportProcessingStatus}
            </if>
            <!-- 帖子状态筛选 -->
            <if test="request.postStatus != null and request.postStatus != ''">
                AND p.post_status = #{request.postStatus}
            </if>
            <!-- 圈子ID筛选 -->
            <if test="request.socialCircleId != null">
                AND p.social_circle_id = #{request.socialCircleId}
            </if>
            <!-- 确保帖子和圈子存在 -->
            AND p.circle_post_id IS NOT NULL
            AND sci.social_circle_id IS NOT NULL
        GROUP BY r.circle_post_id, p.circle_post_id, p.social_circle_id, sci.social_circle_name, 
                 p.post_status, p.user_id, p.created_time, p.circle_post_title, p.circle_post_content,
                 p.post_cover_url, p.post_categ_id, p.location, p.province_name, p.city_name, p.shared_type
        <!-- 动态排序 -->
        <choose>
            <when test="request.orderBy != null and request.orderBy == 'report_count'">
                ORDER BY reportCount 
                <if test="request.orderDirection != null and request.orderDirection == 'ASC'">ASC</if>
                <if test="request.orderDirection == null or request.orderDirection == 'DESC'">DESC</if>
            </when>
            <when test="request.orderBy != null and request.orderBy == 'post_created_time'">
                ORDER BY p.created_time 
                <if test="request.orderDirection != null and request.orderDirection == 'ASC'">ASC</if>
                <if test="request.orderDirection == null or request.orderDirection == 'DESC'">DESC</if>
            </when>
            <when test="request.orderBy != null and request.orderBy == 'first_report_time'">
                ORDER BY firstReportTime 
                <if test="request.orderDirection != null and request.orderDirection == 'ASC'">ASC</if>
                <if test="request.orderDirection == null or request.orderDirection == 'DESC'">DESC</if>
            </when>
            <otherwise>
                <!-- 默认按最新举报时间降序排列 -->
                ORDER BY latestReportTime DESC, reportCount DESC
            </otherwise>
        </choose>
    </select>

</mapper>
