package zx.vanx.user_permiss;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR> Date: 2023/7/517:10
 **/
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {
        "zx.vanx.au_media.client",
        "zx.vanx.as_media.client",
        "zx.vanx.au_chat.client",
        "zx.vanx.ma_manage.client",
        "zx.vanx.social_circle.client",
        "zx.vanx.job_recruit.client"})
@EnableScheduling
@MapperScan("zx.vanx.user_permiss.mapper")
@ComponentScan(basePackages = "zx.vanx")
public class UserPermissApplication {

    public static void main(String[] args) {
        SpringApplication.run(UserPermissApplication.class);
    }

}
