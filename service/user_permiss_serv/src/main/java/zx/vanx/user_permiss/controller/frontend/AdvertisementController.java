package zx.vanx.user_permiss.controller.frontend;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.user_permiss.entity.AdDispForUser;
import zx.vanx.user_permiss.service.AdvertisementService;

import java.util.List;

@Api(tags = "红包系列推送管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/advertisement")
public class AdvertisementController {

    private final AdvertisementService advertisementService;

    /**
     * 查询当前用户是否需要弹出红包广告
     */
    @ApiOperation(value = "查询当前用户是否需要弹出红包广告")
    @PostMapping("/userWhetherRequirePopUpAdvert")
    public ResultData<Boolean> userWhetherRequirePopUpAdvert() {
       Boolean isRequire = advertisementService.userWhetherRequirePopUpAdvert();

       return ResultData.ok(isRequire);
    }

    /**
     * 查询当前用户是否需要弹出红包广告
     */
    @ApiOperation(value = "查询需要弹出红包广告列表")
    @PostMapping("/selectAdPopList")
    public ResultData<List<AdDispForUser>> selectAdPopList() {
        List<AdDispForUser> adDispForUserList = advertisementService.selectAdPopList();
        return ResultData.ok(adDispForUserList);
    }

}
