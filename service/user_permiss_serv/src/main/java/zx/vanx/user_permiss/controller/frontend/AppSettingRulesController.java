package zx.vanx.user_permiss.controller.frontend;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.user_permiss.entity.AppSettingRules;
import zx.vanx.user_permiss.service.AppSettingRulesService;

import java.util.List;


@Api(tags = {"平台系统设置规则表"})
@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/app-setting-rules")
public class AppSettingRulesController {

    private final AppSettingRulesService appSettingRulesService;

    @ApiOperation(value = "查询-单个设置")
    @PostMapping("/one/{appRuleId}")
    public ResultData<AppSettingRules> selectAppSettingRulesOne(@PathVariable(value = "appRuleId") Long appRuleId) {

        AppSettingRules appSettingRulesList = appSettingRulesService.selectAppSettingRulesOne(appRuleId);

        return ResultData.ok(appSettingRulesList);
    }

    @ApiOperation(value = "查询-顶层设置列表")
    @PostMapping("/list/{parentAppRuleId}")
    public ResultData<List<AppSettingRules>> selectAppSettingRulesList(@PathVariable(value = "parentAppRuleId") Long parentAppRuleId) {

        List<AppSettingRules> appSettingRulesList = appSettingRulesService.selectAppSettingRulesList(parentAppRuleId);

        return ResultData.ok(appSettingRulesList);
    }

//    @ApiOperation(value = "添加")
//    @PostMapping("/add")
//    public ResultData<JSONObject> add(@RequestPart(value = "appSettingRules") AppSettingRules appSettingRules,
//                                      @RequestPart(value = "file") MultipartFile file) {
//
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.putOpt("jsonObject", appSettingRules);
//        jsonObject.putOpt("file", file.getOriginalFilename());
//
//        return ResultData.ok(jsonObject);
//    }

}

