package zx.vanx.user_permiss.controller.frontend;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.user_permiss.entity.UserInfo;
import zx.vanx.user_permiss.service.FriendInfoService;

import java.util.List;


@Api(tags = "f、用户-好友")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/friend-info")
public class FriendInfoController  {

    private final FriendInfoService friendInfoService;

    @ApiOperation("查询用户的好友列表")
    @PostMapping("/list")
    public List<UserInfo> queryFriendInfoList() {
        return friendInfoService.queryFriendInfoList(LoginHelper.getUserId());
    }

    @ApiOperation("查询当前用户是否为某作品用户互关朋友")
    @PostMapping("/queryUserTogetherFriends")
    public boolean queryUserTogetherFriends(@RequestParam(value = "mediaUserId") Long mediaUserId) {

        return friendInfoService.queryUserTogetherFriends(mediaUserId);

    }

}