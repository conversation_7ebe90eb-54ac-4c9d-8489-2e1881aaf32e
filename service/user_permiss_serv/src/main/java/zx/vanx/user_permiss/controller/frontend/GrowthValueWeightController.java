package zx.vanx.user_permiss.controller.frontend;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.user_permiss.entity.GrowthValueWeight;
import zx.vanx.user_permiss.service.GrowthValueWeightService;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequiredArgsConstructor
@Api(tags = "用户-成长值-权重")
@RequestMapping("/user_permiss/growth-value-weight")
public class GrowthValueWeightController {

    private final GrowthValueWeightService growthValueWeightService;

    @ApiOperation(value = "保存")
    @PostMapping("/save")
    public ResultData<Void> save(@RequestBody GrowthValueWeight growthValueWeight) {

        growthValueWeightService.save(growthValueWeight);
        return ResultData.ok();

    }

    @ApiOperation(value = "删除")
    @PostMapping("/remove/{weightId}")
    public ResultData<Void> remove(@PathVariable(value = "weightId") Long weightId) {

        growthValueWeightService.removeById(weightId);
        return ResultData.ok();

    }

    @ApiOperation(value = "修改")
    @PostMapping("/update")
    public ResultData<Void> updateById(HttpServletRequest request, @RequestBody GrowthValueWeight growthValueWeight) {

        growthValueWeight.setEditorId(LoginHelper.getUserId());

        growthValueWeightService.updateById(growthValueWeight);

        return ResultData.ok();

    }

    @ApiOperation(value = "列表")
    @PostMapping("/list")
    public ResultData<List<GrowthValueWeight>> list() {
        List<GrowthValueWeight> list = growthValueWeightService.query().list();
        return ResultData.ok(list);
    }

}

