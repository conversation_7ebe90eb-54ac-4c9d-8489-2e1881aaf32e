package zx.vanx.user_permiss.controller.frontend;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.core.page.PageObj;
import zx.vanx.common_util.core.page.QueryPageDto;
import zx.vanx.common_util.exception.ZxException;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.user_permiss.entity.UserInfo;
import zx.vanx.user_permiss.service.IdolFansRelatService;
import zx.vanx.user_permiss.service.UserInfoService;
import zx.vanx.user_permiss.vo.AttentionData;
import zx.vanx.user_permiss.vo.UserAttentionPersonVo;
import zx.vanx.user_permiss.vo.UserFollowersVo;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-25
 */
@Api(tags = "e、用户-粉丝偶像")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/idol-fans-relat")
public class IdolFansRelatController  {

    private final IdolFansRelatService idolFansRelatService;

    private final UserInfoService userInfoService;

    @ApiOperation("关注用户-偶像id")
    @PostMapping("/insert-idol")
    public ResultData<Object> attentionUser(@RequestParam(value = "idolId") Long idolId) {

        // userId 不能为空
        if (idolId == null) {
            throw new ZxException(202, "idolId不能为空");
        }

        UserInfo userInfo = userInfoService.selectOneByUserId(LoginHelper.getUserId());

        if (ObjectUtils.isEmpty(userInfo)) {
            throw new ZxException(20004, "用户不存在");
        }

        idolFansRelatService.attentionUser(LoginHelper.getUserId(),idolId);

        return ResultData.ok().Message("关注成功");
    }

    @ApiOperation("取关用户-偶像id")
    @PostMapping("/user-cancel-attention")
    public ResultData<Object> userCancelAttention(@RequestParam(value = "idolId") Long idolId) {

        idolFansRelatService.userCancelAttention(LoginHelper.getUserId(),idolId);

        return ResultData.ok().Message("取关成功");
    }

    @ApiOperation("查询用户粉丝列表")
    @PostMapping("/user-follower-list")
    public ResultData<PageObj<UserFollowersVo>> queryUserFollowerList(@RequestBody QueryPageDto queryPageDto) {

        PageObj<UserFollowersVo> userFollowerPageObj = idolFansRelatService.queryUserFollowerListWithPage(LoginHelper.getUserId(), queryPageDto);

        return ResultData.ok(userFollowerPageObj);

    }

    @ApiOperation("查询关注人员列表")
    @PostMapping("/user-attention-person-list")
    public ResultData<PageObj<UserAttentionPersonVo>> queryUserAttentionPersonList(@RequestBody QueryPageDto queryPageDto) {

        PageObj<UserAttentionPersonVo> userAttentionPageObj = idolFansRelatService.queryUserAttentionPersonListWithPage(LoginHelper.getUserId(), queryPageDto);

        return ResultData.ok(userAttentionPageObj);
    }

    @ApiOperation("关注用户-vanx号")
    @PostMapping("/insert-vanx")
    public ResultData<Object> attentionUser(@RequestParam(value = "userInternalCode") String userInternalCode) {

        // userId 不能为空
        if (userInternalCode == null) {
            throw new ZxException(20001, "userInternalCode不能为空");
        }

        UserInfo userInfo = userInfoService.getBaseMapper()
                .selectOne(new QueryWrapper<UserInfo>()
                        .eq("user_internal_code", userInternalCode));

        if (ObjectUtils.isEmpty(userInfo)) {
            throw new ZxException(20004, "用户不存在");
        }

        // 偶像id
        Long idolId = userInfo.getUserId();

        idolFansRelatService.attentionUser(LoginHelper.getUserId(),idolId);

        return ResultData.ok().Message("关注成功");

    }


    //通过userId、publish_content_type查询vanx_user_inbox_timeline表的publish_content_id的List
    /**
     * 查询用户关注下的视频 | 图片 | 微博 | 笔记
     */
    @ApiOperation("个人关注-作品列表")
    @PostMapping("/user-list")
    public ResultData<AttentionData> queryUserAttentionList(@RequestParam(value = "fileType") String fileType) {

        // fileType 不能为空
        if (fileType == null || fileType.isEmpty()) {
            throw new ZxException(204, "fileType不能为空");
        }

        AttentionData attentionData = idolFansRelatService.queryUserAttentionList(LoginHelper.getUserId(),fileType);

        return ResultData.ok(attentionData);
    }

}