package zx.vanx.user_permiss.controller.frontend;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.user_permiss.entity.InboxTimeline;
import zx.vanx.user_permiss.service.InboxTimelineService;


@Api(tags = "时间线-记录")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/inbox-timeline")
public class InboxTimelineController {

    private final InboxTimelineService inboxTimelineService;

    @ApiOperation("添加时间线记录表")
    @PostMapping("/save")
    public ResultData<Void> saveInboxTimeline(@RequestBody InboxTimeline inboxTimeline) {
        inboxTimelineService.save(inboxTimeline);
        return ResultData.ok();
    }

    @ApiOperation("通过publishContentId删除时间线记录表中的数据")
    @PostMapping("/delete")
    public boolean deleteInboxTimeline(@RequestParam Long mediaId) {
        inboxTimelineService.remove(new QueryWrapper<InboxTimeline>().eq("publish_content_id",mediaId));
        return true;
    }

}

