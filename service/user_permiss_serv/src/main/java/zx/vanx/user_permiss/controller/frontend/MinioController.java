package zx.vanx.user_permiss.controller.frontend;

import cn.hutool.json.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.common_util.utils.StringUtil;
import zx.vanx.service.MinioService;
import zx.vanx.vo.ImageData;

@Api(tags = "a、文件上传")
@RequiredArgsConstructor
@RestController
@RequestMapping("/user_permiss/minio")
public class MinioController  {

    private final MinioService minioService;

    @ApiOperation("系统文件上传")
    @PostMapping("/upload-system-file")
    public JSONObject uploadSystemFile(MultipartFile file) {

        ImageData imageData = minioService.uploadFile(file);

        JSONObject json = new JSONObject();
        json.putOpt("url", imageData.getUrl());
        return json;
    }

    @ApiOperation("系统文件删除")
    @PostMapping("/remove-system-file")
    public ResultData<?> removeSystemFile(@RequestParam(value = "url") String url) {

        minioService.removeObject(StringUtil.extractFilePath(url));

        return ResultData.ok().Message("删除成功");
    }

    @ApiOperation(value = "系统用户个人文件上传")
    @PostMapping("/upload-system-user-file")
    
    public ResultData<JSONObject> uploadUserSystemFile(MultipartFile file) {
        ImageData imageData = minioService.uploadFile(file, LoginHelper.getUserId());

        JSONObject json = new JSONObject();
        json.putOpt("minioName", imageData.getFileName());
        json.putOpt("url", imageData.getUrl());
        return ResultData.ok(json);

    }

}
