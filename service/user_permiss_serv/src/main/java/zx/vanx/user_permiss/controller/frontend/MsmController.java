package zx.vanx.user_permiss.controller.frontend;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.user_permiss.service.MsmService;


@Api(tags = "b、用户-短信")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/msm")
public class MsmController {

    private final MsmService msmService;

    @ApiOperation("发送-登录验证码")
    @GetMapping("/send-login-code")
    public ResultData<String> sendLoginCode(@RequestParam("phone") String phone) {
        return msmService.sendLoginCode(phone);
    }

    @ApiOperation("发送-注册证码")
    @GetMapping("/send-register-code")
    public ResultData<String> sendRegisterCode(@RequestParam("phone") String phone) {

        return msmService.sendRegisterCode(phone);

    }

    @ApiOperation("test")
    @GetMapping("/test")
    public ResultData<String> test(@RequestParam("test") String test) {

        msmService.process2(test);

        return ResultData.ok();

    }


}
