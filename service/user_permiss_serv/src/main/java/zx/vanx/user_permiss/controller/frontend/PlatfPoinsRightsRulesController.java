package zx.vanx.user_permiss.controller.frontend;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.utils.HTTPJsonUtil;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.user_permiss.entity.PlatfPoinsRightsRules;
import zx.vanx.user_permiss.request.PlatfPoinsRightsAddRequest;
import zx.vanx.user_permiss.service.PlatfPoinsRightsRulesService;
import zx.vanx.user_permiss.vo.UserRightsVo;
import zx.vanx.user_permiss.vo.query.PlatformPoinsRulesQueryVo;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


@Api(tags = "d、用户-等级-权益")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/platform-poins-rules")
public class PlatfPoinsRightsRulesController  {

    private final PlatfPoinsRightsRulesService platformPoinsRulesService;

    /**
     * 查询该用户能够添加的文件总数，通过文件类型、行为、购物积分、游戏积分
     * @param userId 用户id
     * @param userBehaviorType 文件类型
     * @return 可添加数
     */
    @ApiOperation("查询-用户行为类型-允许添加次数")
    @PostMapping("/total")
    public Long selectTotalRuleObjectValue(@RequestParam(value = "userId") Long userId,
                                           @RequestParam(value = "userBehaviorType") String userBehaviorType) {

        return platformPoinsRulesService.selectTotalRuleObjectValue(userId,userBehaviorType);

    }

    @ApiOperation("app查询用户等级权益信息")
    @GetMapping("/level-right")
    
    public ResultData<UserRightsVo> selectLevelRights() {

        UserRightsVo userRightsVo = platformPoinsRulesService.selectLevelRights(LoginHelper.getUserId());

        return ResultData.ok(userRightsVo);

    }

    /**
     * 添加平台积分权益
     * @param obj 获取添加平台积分规则值对象
     * @param file 所属类别图标文件
     * @return 结果
     */
    @ApiOperation("添加")
    @PostMapping("/save")
    public ResultData<Void> save(@RequestParam(value = "obj") String obj,
                                 @RequestPart(value = "file") MultipartFile file) {

        // 获取添加平台积分规则值对象
        PlatfPoinsRightsAddRequest platfPoinsRightsAddRequest = HTTPJsonUtil.parseObj(obj, PlatfPoinsRightsAddRequest.class);

        Long currentUserId = LoginHelper.getUserId();
        platfPoinsRightsAddRequest.setCreatorId(currentUserId);

        // 添加平台积分权益
        platformPoinsRulesService.savePlatfPointRights(platfPoinsRightsAddRequest,file);

        return ResultData.ok();
    }

    /**
     * 根据等级id查询-权益列表
     * @param levelInfoId 等级id
     * @return 权益列表
     */
    @ApiOperation("根据等级id查询-权益列表")
    @PostMapping("/right-list")
    public ResultData<List<PlatfPoinsRightsRules>> queryRightList(
                                 @RequestParam(value = "levelInfoId") Long levelInfoId) {

        List<PlatfPoinsRightsRules> platformPoinsRulesList = platformPoinsRulesService.queryRightList(levelInfoId);

        return ResultData.ok(platformPoinsRulesList);
    }

    /**
     * 查询该等级未添加用户行为列表
     * @param levelInfoId 等级id
     * @return 用户行为列表
     */
    @ApiOperation("该等级未添加用户行为列表")
    @PostMapping("/user-behavior-list")
    public ResultData<List<String>> queryUserBehaviorList(@RequestParam(value = "levelInfoId") Long levelInfoId) {
        List<String> userBehaviorList = platformPoinsRulesService.queryUserBehaviorList(levelInfoId);
        return ResultData.ok(userBehaviorList);
    }

    /**
     * 查询该等级未添加权益名称列表
     * @param levelInfoId 等级id
     * @return 权益名称列表
     */
    @ApiOperation("该等级未添加权益名称列表")
    @PostMapping("/user-roleName-list")
    public ResultData<List<String>> queryUserroleNameList(@RequestParam(value = "levelInfoId") Long levelInfoId) {
        List<String> userRoleNameList = platformPoinsRulesService.queryUserRoleNameList(levelInfoId);
        return ResultData.ok(userRoleNameList);
    }

    /**
     * 查询权益单位
     * @return 权益单位列表
     */
    @ApiOperation("查询权益单位列表")
    @PostMapping("/rule-object-type-list")
    public ResultData<List<String>> queryRuleObjectTypeList() {
        List<String> ruleObjectTypeList = platformPoinsRulesService.queryRuleObjectTypeList();
        return ResultData.ok(ruleObjectTypeList);
    }

    /**
     * 修改权益
     * @param request 请求
     * @param platformPoinsRules 权益
     * @return 结果
     */
    @ApiOperation("修改")
    @PostMapping("/modified")
    public ResultData<Void> modified(HttpServletRequest request,
                                 @RequestBody PlatfPoinsRightsRules platformPoinsRules) {

        Long userId = LoginHelper.getUserId();
        platformPoinsRules.setEditorId(userId);

        platformPoinsRulesService.updateById(platformPoinsRules);

        return ResultData.ok();
    }

    /**
     * 权益列表
     * @param platformPoinsRulesQueryVo 查询条件
     * @return 结果
     */
    @ApiOperation("列表")
    @PostMapping("/list")
    public ResultData<List<PlatfPoinsRightsRules>> list(@RequestBody PlatformPoinsRulesQueryVo platformPoinsRulesQueryVo) {

        QueryWrapper<PlatfPoinsRightsRules> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_level_type",platformPoinsRulesQueryVo.getUserLevelType())
                .eq("user_level_name",platformPoinsRulesQueryVo.getUserLevelName())
                .orderByAsc("rule_object_type")
                .orderByAsc("rule_object_value")
                .orderByDesc("created_time");

        List<PlatfPoinsRightsRules> platformPoinsRulesList = platformPoinsRulesService.list(queryWrapper);

        return ResultData.ok(platformPoinsRulesList);
    }

    /**
     * 删除
     * @param poinsRuleId 权益id
     * @return 结果
     */
    @ApiOperation("删除")
    @PostMapping("/remove/{poinsRuleId}")
    public ResultData<Void> remove(@PathVariable(value = "poinsRuleId") Long poinsRuleId) {

        platformPoinsRulesService.removeById(poinsRuleId);
        return ResultData.ok();
    }


    /**
     * 查询所有权益名称列表
     * @return 权益名称列表
     */
    @ApiOperation("查询所有权益名称列表")
    @PostMapping("/query-rights-name-list")
    public ResultData<List<String>> queryRightsNameList() {

        List<String> rightsList = platformPoinsRulesService.queryRightsNameList();

        return ResultData.ok(rightsList);
    }

}
