package zx.vanx.user_permiss.controller.frontend;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import zx.vanx.user_permiss.entity.PlatfPointsCategory;
import zx.vanx.user_permiss.service.PlatfPointsCategoryService;

/**
 * <p>
 * Vanx系统的积分的类别表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Api(tags = "积分类别")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/platf-points-category")
public class PlatfPointsCategoryController {

    private final PlatfPointsCategoryService platfPointsCategoryService;

    @ApiOperation("查询积分类别-通过类别名称")
    @PostMapping("/query-one")
    public PlatfPointsCategory queryPlatfPointsCategoryByInternalPointCategName(@RequestParam(value = "internalPointCategName") String internalPointCategName) {
        return platfPointsCategoryService.getPlatfPointsCategoryByInternalPointCategName(internalPointCategName);
    }

}

