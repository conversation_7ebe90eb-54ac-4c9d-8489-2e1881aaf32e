package zx.vanx.user_permiss.controller.frontend;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.user_permiss.entity.PlatfUserLabel;
import zx.vanx.user_permiss.service.PlatfUserLabelService;

import java.util.List;

@Api(tags = "g、用户-标签")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/platf-user-label")
public class PlatfUserLabelController {

    private final PlatfUserLabelService platfUserLabelService;

    @ApiOperation("未使用公共标签列表")
    @PostMapping("/public-list")
    public ResultData<List<PlatfUserLabel>> selectPublicList() {

        List<PlatfUserLabel> platfUserLabelList = platfUserLabelService.selectPublicList();

        return ResultData.ok(platfUserLabelList);

    }

    @ApiOperation("所有公共标签列表")
    @PostMapping("/all-public-list")
    public ResultData<List<PlatfUserLabel>> selectAllPublicList() {

        List<PlatfUserLabel> platfUserLabelList = platfUserLabelService.selectAllPublicList();

        return ResultData.ok(platfUserLabelList);

    }

    @ApiOperation("用户标签列表")
    @PostMapping("/user-label-list")
    public ResultData<List<PlatfUserLabel>> selectUserLabelList() {

        List<PlatfUserLabel> platfUserLabelList = platfUserLabelService.selectUserLabelList(LoginHelper.getUserId());
        return ResultData.ok(platfUserLabelList);
    }

    @ApiOperation("添加用户标签")
    @PostMapping("/add-user-label")
    public ResultData<Void> addUserLabel(@RequestBody List<PlatfUserLabel> platfUserLabel) {

        platfUserLabelService.addUserLabel(platfUserLabel);

        return ResultData.ok();
    }

    @ApiOperation("删除用户标签")
    @PostMapping("/delete-user-label")
    public ResultData<Void> deleteUserLabel(@RequestBody PlatfUserLabel platfUserLabel) {

        platfUserLabelService.deleteUserLabel(platfUserLabel);

        return ResultData.ok();
    }



}

