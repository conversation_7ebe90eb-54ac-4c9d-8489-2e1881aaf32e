package zx.vanx.user_permiss.controller.frontend;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.user_permiss.entity.UserAddress;
import zx.vanx.user_permiss.service.UserAddressService;

import java.util.List;

@Api(tags = "f、用户-地址信息")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/user-address")
public class UserAddressController  {

    private final UserAddressService userAddressService;

    @ApiOperation("修改-用户故乡")
    @PostMapping("/update-user-hometown")
    public ResultData<Void> saveOrUpdateUserHometown(@RequestBody UserAddress userAddress) {

        // 获取用户id
        userAddress.setEditorId(LoginHelper.getUserId());
        userAddressService.saveOrUpdateUserHometown(userAddress);

        return ResultData.ok();
    }

    @ApiOperation("修改-用户所在地")
    @PostMapping("/update-user-location")
    public ResultData<Void> saveOrUpdateUserLocation(@RequestBody UserAddress userAddress) {

        // 获取用户id
        userAddress.setEditorId(LoginHelper.getUserId());
        userAddressService.saveOrUpdateUserLocation(userAddress);

        return ResultData.ok();
    }

    @ApiOperation("查询用户收件地址列表")
    @PostMapping("/query-user-recieve-address-list")
    public ResultData<List<UserAddress>> queryUserRecieveAddressList() {

        List<UserAddress> userAddressList = userAddressService.queryUserRecieveAddressList(LoginHelper.getUserId());
        return ResultData.ok(userAddressList);
    }


    @ApiOperation("新增或修改用户收件地址")
    @PostMapping("/save-or-update-recieve-address")
    public ResultData<Void> saveOrUpdateUserRecieveAddress(@RequestBody UserAddress userAddress) {
        // 获取用户id
        userAddress.setEditorId(LoginHelper.getUserId());
        userAddressService.saveOrUpdateUserRecieveAddress(userAddress);
        return ResultData.ok();
    }

    @ApiOperation("删除用户收件地址")
    @PostMapping("/remove-recieve-address")
    public ResultData<Void> removeUserRecieveAddress(@RequestBody UserAddress userAddress) {
        // 获取用户id
        userAddress.setEditorId(LoginHelper.getUserId());
        userAddressService.removeUserRecieveAddress(userAddress);
        return ResultData.ok();
    }
}
