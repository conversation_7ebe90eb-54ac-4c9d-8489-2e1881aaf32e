package zx.vanx.user_permiss.controller.frontend;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.core.page.PageObj;
import zx.vanx.common_util.exception.ZxException;
import zx.vanx.common_util.utils.HTTPJsonUtil;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.user_permiss.entity.UserCompanyVerifyInfo;
import zx.vanx.user_permiss.entity.UserVerifyInfo;
import zx.vanx.user_permiss.enums.ArtificialPersonStatusEnum;
import zx.vanx.user_permiss.request.CompanyAuthUserInfoRequest;
import zx.vanx.user_permiss.request.PersonalAuthUserInfoRequest;
import zx.vanx.user_permiss.service.UserService;
import zx.vanx.user_permiss.vo.*;

import java.util.List;

@Api(tags = "b、用户-认证模块")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/user")
public class UserCertificationController  {

    private final UserService userService;

    @ApiOperation("添加求职者个人招募身份")
    
    @PostMapping("/add-user-job-seek-identity")
    public ResultData<Object> addUserJobSeekIdentity() {
        userService.addUserJobSeekIdentity(LoginHelper.getUserId());
        return ResultData.ok().Message("添加成功");
    }

    @ApiOperation("添加求职者企业招募身份")
    
    @PostMapping("/add-user-company-identity")
    public ResultData<Object> addUserCompanyIdentity() {
        userService.addUserCompanyIdentity(LoginHelper.getUserId());
        return ResultData.ok().Message("添加成功");
    }

    @ApiOperation("查询用户是否存在求职者个人招募身份")
    
    @PostMapping("/query-user-is-job-seek-identity")
    public ResultData<Boolean> queryUserIsJobSeekIdentity() {

        Boolean isJobSeekIdentity = userService.queryUserIsJobSeekIdentity(LoginHelper.getUserId());
        return ResultData.ok(isJobSeekIdentity);
    }

    @ApiOperation("查询用户是否存在求职者企业招募身份")
    
    @PostMapping("/query-user-is-company-identity")
    public ResultData<Boolean> queryUserIsCompanyIdentity() {
        Boolean isCompanyIdentity = userService.queryUserIsCompanyIdentity(LoginHelper.getUserId());
        return ResultData.ok(isCompanyIdentity);
    }

    @ApiOperation("删除用户企业认证信息")
    @PostMapping("/removeUserCompanyAuthInfo")
    
    public ResultData<Object> removeUserCompanyAuthInfo(@RequestBody UserCompanyVerifyInfo userCompanyVerifyInfo) {
        userCompanyVerifyInfo.setEditorId(LoginHelper.getUserId());
        userService.removeUserCompanyAuthInfo(userCompanyVerifyInfo);
        return ResultData.ok().Message("删除成功");
    }

    @ApiOperation("删除用户个人认证信息")
    @PostMapping("/removeUserPersonalAuthInfo")
    
    public ResultData<Object> removeUserPersonalAuthInfo(@RequestBody UserVerifyInfo userVerifyInfo) {
        userVerifyInfo.setEditorId(LoginHelper.getUserId());
        userService.removeUserPersonalAuthInfo(userVerifyInfo);
        return ResultData.ok().Message("删除成功");
    }

    @ApiOperation("查询所有企业认证记录列表")
    @PostMapping("/queryUserCompanyAuthInfoList")
    
    public ResultData<List<UserCompanyVerifyInfo>> queryUserCompanyAuthInfoList() {
        List<UserCompanyVerifyInfo> userVerifyInfoList = userService.queryUserCompanyAuthInfoList(LoginHelper.getUserId());
        return ResultData.ok(userVerifyInfoList);
    }

    @ApiOperation("查询所有个人认证记录列表")
    @PostMapping("/queryUserPersonalAuthInfoList")
    
    public ResultData<List<UserVerifyInfo>> queryUserPersonalAuthInfoList() {
        List<UserVerifyInfo> userVerifyInfoList = userService.queryUserPersonalAuthInfoList(LoginHelper.getUserId());
        return ResultData.ok(userVerifyInfoList);
    }

    @ApiOperation("查询用户是否个人认证")
    @PostMapping("/queryUserIsPersonalAuth")
    public ResultData<PersonalAuthInfoVo> queryUserIsPersonalAuth() {
        PersonalAuthInfoVo personalAuthInfoVo = userService.queryUserIsPersonalAuth(LoginHelper.getUserId());
        return ResultData.ok(personalAuthInfoVo);
    }

    @ApiOperation("查询用户是否企业认证")
    @PostMapping("/queryUserIsCompanyAuth")
    public ResultData<CompanyAuthInfoVo> queryUserIsCompanyAuth() {
        CompanyAuthInfoVo companyAuthInfoVo = userService.queryUserIsCompanyAuth(LoginHelper.getUserId());
        return ResultData.ok(companyAuthInfoVo);
    }

    @ApiOperation("企业认证状态修改")
    @PostMapping("/company-auth-status-update")
    public ResultData<Object> companyAuthStatusUpdate(@RequestParam(value = "userComVerifyId") Long userComVerifyId,
                                                      @RequestParam(value = "artificialPersonStatus") String artificialPersonStatus,
                                                      @RequestParam(value = "addSource") String addSource,
                                                      @RequestParam(value = "rejectReason",required = false) String rejectReason) {

        if (!ArtificialPersonStatusEnum.isValid(artificialPersonStatus)) {
            throw new ZxException(206,"artificialPersonStatus参数必须为"+ ArtificialPersonStatusEnum.DAI_SHEN_HE +","+ ArtificialPersonStatusEnum.TONG_GUO +","+ ArtificialPersonStatusEnum.BO_HUI+"之一");
        }
        userService.companyAuthStatusUpdate(LoginHelper.getUserId(),userComVerifyId,artificialPersonStatus,addSource,rejectReason);
        return ResultData.ok("修改成功");
    }

    @ApiOperation("个人认证状态修改")
    @PostMapping("/personal-auth-status-update")
    public ResultData<Object> personalAuthStatusUpdate(@RequestParam(value = "userVerifyId") Long userVerifyId,
                                                       @RequestParam(value = "artificialPersonStatus") String artificialPersonStatus,
                                                       @RequestParam(value = "addSource") String addSource,
                                                       @RequestParam(value = "rejectReason",required = false) String rejectReason) {

        if (!ArtificialPersonStatusEnum.isValid(artificialPersonStatus)) {
            throw new ZxException(206,"artificialPersonStatus参数必须为"+ ArtificialPersonStatusEnum.DAI_SHEN_HE +","+ ArtificialPersonStatusEnum.TONG_GUO +","+ ArtificialPersonStatusEnum.BO_HUI+"之一");
        }
        userService.personalAuthStatusUpdate(LoginHelper.getUserId(),userVerifyId,artificialPersonStatus,addSource,rejectReason);
        return ResultData.ok("修改成功");
    }

    @ApiOperation("个人认证用户列表")
    @PostMapping("/personal-auth-user-list")
    public ResultData<PageObj<PersonalAuthUserInfo>> personalAuthUserList(@RequestBody PersonalAuthUserInfoRequest personalAuthUserInfoRequest) {

        PageObj<PersonalAuthUserInfo> personalAuthUserInfoPageObj = userService.personalAuthUserList(personalAuthUserInfoRequest);
        return ResultData.ok(personalAuthUserInfoPageObj);
    }

    @ApiOperation("企业认证用户列表")
    @PostMapping("/company-auth-user-list")
    public ResultData<PageObj<CompanyAuthUserInfo>> companyAuthUserList(@RequestBody CompanyAuthUserInfoRequest companyAuthUserInfoRequest) {

        PageObj<CompanyAuthUserInfo> personalAuthUserInfoPageObj = userService.companyAuthUserList(companyAuthUserInfoRequest);
        return ResultData.ok(personalAuthUserInfoPageObj);
    }

    @ApiOperation("用户个人认证")
    @PostMapping("/user-personal-auth")
    public ResultData<Object> userPersonalAuth(@RequestPart(value = "idPicFrontFile") MultipartFile idPicFrontFile,
                                               @RequestPart(value = "idPicBackFile") MultipartFile idPicBackFile,
                                               @RequestPart(value = "obj") String obj) {
        UserVerifyInfo userVerifyInfo = HTTPJsonUtil.parseObj(obj, UserVerifyInfo.class);
        userService.userPersonalAuth(LoginHelper.getUserId(),idPicFrontFile,idPicBackFile,userVerifyInfo);
        return ResultData.ok().Message("用户个人认证成功");
    }

    @ApiOperation("用户企业认证")
    @PostMapping("/user-company-auth")
    public ResultData<Object> userCompanyAuth(@RequestPart(value = "idPicFrontFile") MultipartFile idPicFrontFile,
                                              @RequestPart(value = "idPicBackFile") MultipartFile idPicBackFile,
                                              @RequestPart(value = "businessPicFile") MultipartFile businessPicFile,
                                              @RequestPart(value = "obj") String obj) {
        UserCompanyVerifyInfo userCompanyVerifyInfo = HTTPJsonUtil.parseObj(obj, UserCompanyVerifyInfo.class);
        userService.userCompanyAuth(LoginHelper.getUserId(),idPicFrontFile,idPicBackFile,businessPicFile,userCompanyVerifyInfo);
        return ResultData.ok().Message("用户企业认证成功");
    }

    @ApiOperation("用户切换身份列表查询")
    @PostMapping("/user-identity-switch-list")
    public ResultData<List<UserIdentitySwitch>> userIdentitySwitchList(@RequestParam(value = "roleCategName") String roleCategName) {

        List<UserIdentitySwitch> platfRolesList = userService.userIdentitySwitchList(LoginHelper.getUserId(),roleCategName);
        return ResultData.ok(platfRolesList);
    }

    @ApiOperation("用户身份切换")
    @PostMapping("/user-identity-switch")
    public ResultData<Object> userIdentitySwitch(@RequestParam(value = "roleName") String roleName) {

        userService.userIdentitySwitch(LoginHelper.getUserId(),roleName);
        return ResultData.ok().Message("切换成功");
    }
}
