package zx.vanx.user_permiss.controller.frontend;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaCheckRole;
import cn.hutool.core.lang.Validator;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.core.page.PageObj;
import zx.vanx.common_util.enums.StatusEnums;
import zx.vanx.common_util.exception.ZxException;
import zx.vanx.common_util.utils.ExcelFIleUtils;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.service.MinioService;
import zx.vanx.user_permiss.entity.PlatfPoinsRightsRules;
import zx.vanx.user_permiss.entity.UserExtendInfo;
import zx.vanx.user_permiss.entity.UserInfo;
import zx.vanx.user_permiss.entity.UserInfoFast;
import zx.vanx.user_permiss.excel.UserInfoExcel;
import zx.vanx.user_permiss.model.LoginUser;
import zx.vanx.user_permiss.redis.service.UserInfoFastRedisService;
import zx.vanx.user_permiss.request.ElecResourceUserRequest;
import zx.vanx.user_permiss.request.UserListRequest;
import zx.vanx.user_permiss.service.RedisService;
import zx.vanx.user_permiss.service.UserExtendInfoService;
import zx.vanx.user_permiss.service.UserService;
import zx.vanx.user_permiss.utils.StringUtil;
import zx.vanx.user_permiss.vo.*;
import zx.vanx.util.EasyExcelUtils;
import zx.vanx.vo.ImageData;

import java.util.List;

@Api(tags = "a、用户-登录-基本信息")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/user")
public class UserController {

    private final UserService userService;

    private final UserExtendInfoService userExtendInfoService;

    private final RedisService redisService;

    private final MinioService minioService;

    private final UserInfoFastRedisService userInfoFastRedisService;

    @ApiOperation("验证token是否过期")
    @PostMapping("verify-token")
    @SaCheckPermission(value = "statistics:visit:list")
    public ResultData<?> validToken() {

        return ResultData.ok(LoginHelper.isTokenValid());
    }

    @ApiOperation("test权限statistics:visit:list")
    @PostMapping("testMenuPermiss1")
    @SaCheckPermission("statistics:visit:list")
    public ResultData<?> test1() {
        return ResultData.ok("testMenuPermiss");
    }

    @ApiOperation("test菜单权限product:comment:list")
    @PostMapping("testMenuPermiss2")
    @SaCheckPermission("product:comment:list")
    public ResultData<?> test2() {
        return ResultData.ok("testMenuPermiss");
    }

    @ApiOperation("test角色权限seller_admin")
    @PostMapping("testRolePermiss1")
    @SaCheckRole("seller_admin")
    public ResultData<?> testRolePermissSellerAdmin() {
        return ResultData.ok("testRolePermiss");
    }

    @ApiOperation("test角色权限buyer")
    @PostMapping("testRolePermiss2")
    @SaCheckRole("buyer")
    public ResultData<?> testRolePermissBuyer() {
        return ResultData.ok("testRolePermiss");
    }

    @ApiOperation("测试获取LoginUser")
    @GetMapping("/getLoginUser")
    public ResultData<?> getLoginUser() {
        throw new ZxException(203, "登录错误");
    }

    @ApiOperation("测试获取UserInfoFast")
    @GetMapping("/getUserInfoFast")
    public ResultData<?> getUserInfoFast() {
        UserInfoFast userInfoFast = userInfoFastRedisService.getUserInfoFastByUserId(LoginHelper.getUserId());
        return ResultData.ok(userInfoFast);
    }

    @ApiOperation("电子资源用户列表")
    @PostMapping("/elec-resource-user-list")
    public ResultData<PageObj<ElecResourceUserVo>> elecResourceUserList(
            @RequestBody ElecResourceUserRequest elecResourceUserRequest) {

        PageObj<ElecResourceUserVo> userElecResourcePageObj = userService.elecResourceUserList(LoginHelper.getUserId(),
                elecResourceUserRequest);

        return ResultData.ok(userElecResourcePageObj);
    }

    @ApiOperation("查询用户附近的人")
    @PostMapping("/select-nearby-user")
    public ResultData<List<NearPerson>> selectNearbyUser() {

        List<NearPerson> userInfoVoList = userService.selectNearbyUser(LoginHelper.getUserId());
        return ResultData.ok(userInfoVoList);

    }

    @ApiOperation(value = "导入用户基本信息")
    @PostMapping("import-user-info")
    public ResultData<Object> importUserInfo(MultipartFile file) {
        List<UserInfoExcel> listByFile = EasyExcelUtils.getListByFile(file, UserInfoExcel.class);
        userService.importUserInfo(listByFile);
        return ResultData.ok().Message("导入成功");
    }

    @ApiOperation("本机-一键登录")
    @PostMapping("/login-tel")
    public ResultData<JSONObject> userLoginOnlyTel(
            @ApiParam(value = "手机号", required = true, allowableValues = "15720801803,13781598361,13193977273,15936057094,13182984356") @RequestParam(value = "tel") String tel,
            @ApiParam(value = "角色名称：普通买家用户", required = true, allowableValues = "普通买家用户,普通卖家用户,平台超级管理用户,平台普通管理用户,卖家普通管理用户,卖家区域管理用户,卖家超级管理用户") @RequestParam(value = "roleName") String roleName) {

        if (!Validator.isMobile(tel)) {
            throw new ZxException(402, "不是一个手机号");
        }

        if (StringUtils.isEmpty(tel)) {
            throw new ZxException(401, "手机号为空");
        }
        return userService.userLoginOnlyTel(tel, roleName);
    }

    @ApiOperation("手机号-密码登录")
    @PostMapping("/login-tel-pwd")
    public ResultData<JSONObject> userLoginTel(@RequestParam(value = "tel") String tel,
            @RequestParam(value = "registPsw") String registPsw, @RequestParam(value = "roleName") String roleName) {
        // 验证手机号是否为空
        if (StringUtils.isEmpty(tel)) {
            throw new ZxException(401, "手机号为空");
        }
        // HuTool工具类验证手机号
        if (!Validator.isMobile(tel)) {
            throw new ZxException(402, "不是一个手机号");
        }
        return userService.userLoginTel(tel, registPsw, roleName);
    }

    @ApiOperation("手机号-验证码-登录")
    @PostMapping("/login-code")
    public ResultData<JSONObject> loginCode(@RequestParam(value = "tel") String tel,
            @RequestParam(value = "code") String code, @RequestParam(value = "roleName") String roleName) {

        if (StringUtil.isNotEmpty(tel, code)) {
            // HuTool工具类验证手机号
            if (!Validator.isMobile(tel)) {
                throw new ZxException(402, "不是一个手机号");
            }
            // code必须为数字
            if (!Validator.isNumber(code)) {
                throw new ZxException(403, "验证码必须为数字");
            }
            // roleName必须为中文字符串
            if (!Validator.isChinese(roleName)) {
                throw new ZxException(404, "roleName必须为字符串");
            }
            return userService.loginCode(tel, code, roleName);
        }
        // 手机号或验证码为空
        return ResultData.error(StatusEnums.LOGIN_TEL_OR_CODE_ISEMPTY.getMsg());
    }

    @ApiOperation("手机号-验证码-注册")
    @PostMapping("/register")
    public ResultData<Void> register(@RequestParam(value = "tel") String tel,
            @RequestParam(value = "registPsw") String registPsw, @RequestParam(value = "roleName") String roleName,
            @RequestParam(value = "code") String code) {
        if (StringUtil.isNotEmpty(tel, registPsw, code)) {
            userService.register(tel, registPsw, code, roleName);
            return ResultData.ok();
        }
        return ResultData.error(StatusEnums.LOGIN_TEL_OR_CODE_ISEMPTY.getMsg());
    }

    @ApiOperation("根据用户id查询权益列表-测试接口")
    @GetMapping("selectUserLevelRuleList")
    public ResultData<List<PlatfPoinsRightsRules>> selectUserLevelRuleList(@RequestParam(value = "userId") Long useId) {
        return ResultData.ok(redisService.selectUserRights(useId));
    }

    @ApiOperation("获取-用户详情")
    @PostMapping("/getUserDetail")
    public ResultData<UserDetail> getUserDetail() {
        // 获取用户id
        LoginUser loginUser = LoginHelper.getLoginUser();
        UserDetail userDetail = userService.getUserDetail(loginUser.getUserId());
        return ResultData.ok(userDetail);
    }

    @ApiOperation("获取-用户详情（userId）")
    @PostMapping("/getUserDetail/{userId}")
    public ResultData<UserDetail> getUserDetailByUserId(@PathVariable Long userId) {
        // 获取用户id
        UserDetail userDetail = userService.getUserDetail(userId);
        return ResultData.ok(userDetail);
    }

    @ApiOperation("获取-用户列表")
    @PostMapping("/getManagerUserList")

    public ResultData<PageObj<ManagerUserInfoVo>> getManagerUserInfoVoList(
            @RequestBody UserListRequest userListRequest) {

        // 验证用户角色是否为超级管理角色
        if (!userService.isSuperAdmin(LoginHelper.getUserId())) {
            throw new ZxException(403, "无权限操作");
        }
        // 获取用户id
        PageObj<ManagerUserInfoVo> userInfoVoList = userService.getManagerUserInfoVoList(userListRequest);
        return ResultData.ok(userInfoVoList);
    }

    @ApiOperation(value = "测试链接获取文件")
    @PostMapping("testFile")
    public ResultData<ImageData> testFile(@RequestParam(value = "targetDir") String targetDir,
            @RequestParam(value = "fileFuzzyName") String fileFuzzyName) {

        String systemPath = "C:\\Users\\<USER>\\Desktop";

        MultipartFile multipartFiles = ExcelFIleUtils.getMultipartFile(systemPath, targetDir, fileFuzzyName);
        ImageData imageData = minioService.uploadFile(multipartFiles, "test");
        return ResultData.ok(imageData);
    }

    /**
     * 查询-用户扩展信息
     * 
     * @param userId 用户id
     * @return 用户基本信息
     */
    @ApiOperation("查询-用户扩展信息")
    @PostMapping("/queryUserExtendInfoByUserId")
    public ResultData<UserExtendInfo> queryUserExtendInfoByUserId(@RequestParam(value = "userId") Long userId) {
        UserExtendInfo userExtendInfo = userExtendInfoService.getBaseMapper()
                .selectOne(new QueryWrapper<UserExtendInfo>().eq("user_id", userId));
        return ResultData.ok(userExtendInfo);
    }

    /**
     * 修改-用户基本信息
     * 
     * @param userInfo 用户基本信息
     * @return 修改结果
     */
    @ApiOperation("修改-用户基本信息")
    @PostMapping("/update-user-info")
    public ResultData<Void> saveOrUpdateUserInfo(@RequestBody UserInfo userInfo) {

        // 获取用户id
        Long userId = LoginHelper.getUserId();
        // userInfo不能为空
        if (ObjectUtils.isEmpty(userInfo)) {
            throw new ZxException(501, "userInfo不能为空");
        }

        userInfo.setEditorId(userId);
        userInfo.encrypt();
        userService.updateUserInfo(userInfo);
        return ResultData.ok();
    }

    /**
     * app根据vanx号查询用户基本信息
     * 
     * @param vanxId vanx号
     * @return 用户基本信息
     */
    @ApiOperation("app根据vanx号查询用户基本信息")
    @PostMapping("/queryUserInfo/{vanxId}")

    public ResultData<UserInfoVo> queryUserInfoByVanxId(@PathVariable(value = "vanxId") String vanxId) {

        UserInfoVo userInfoVo = userService.queryUserInfoByVanxId(LoginHelper.getUserId(), vanxId);
        return ResultData.ok(userInfoVo);
    }

    /**
     * 根据用户id查询用户身份信息
     * 
     * @param userId 用户id
     * @return 用户基本信息
     */
    @ApiOperation("根据用户id查询用户身份信息")
    @PostMapping("/queryUserIdentityInfo")
    public UserIdentityConfirmVo queryUserIdentityInfo(@RequestParam(value = "userId") Long userId) {

        return userService.queryUserIdentityInfo(userId);
    }

    @ApiOperation("查询积分中心用户基本信息")
    @PostMapping("/integral-center-user-base-info")

    public ResultData<IntegralCenterUserBaseInfo> queryIntegralCenterUserBaseInfo() {
        IntegralCenterUserBaseInfo integralCenterUserBaseInfo = userService
                .queryIntegralCenterUserBaseInfo(LoginHelper.getUserId());
        return ResultData.ok(integralCenterUserBaseInfo).Message("查询成功");
    }

    @ApiOperation("用户注销")

    @PostMapping("/user-logout")
    public ResultData<Object> userLogout() {
        userService.userLogout(LoginHelper.getUserId());
        return ResultData.ok().Message("用户注销成功");
    }

}
