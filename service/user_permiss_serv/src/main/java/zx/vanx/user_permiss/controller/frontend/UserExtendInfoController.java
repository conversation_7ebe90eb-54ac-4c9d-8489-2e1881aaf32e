package zx.vanx.user_permiss.controller.frontend;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.user_permiss.entity.UserExtendInfo;
import zx.vanx.user_permiss.service.UserExtendInfoService;


@Api(tags = "用户-扩展信息")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/user-extend-info")
public class UserExtendInfoController {

    private final UserExtendInfoService userExtendInfoService;

    @ApiOperation("修改-用户扩展信息")
    @PostMapping("/update-user-extend-info")
    public ResultData<Void> saveOrUpdateUserExtendInfo(@RequestBody UserExtendInfo userExtendInfo) {

        // 获取用户id
        Long userId = LoginHelper.getUserId();
        userExtendInfo.setEditorId(userId);
        userExtendInfoService.saveOrUpdateUserExtendInfo(userExtendInfo);

        return ResultData.ok();
    }
}
