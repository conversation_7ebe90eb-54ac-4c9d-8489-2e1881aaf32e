package zx.vanx.user_permiss.controller.frontend;


import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.user_permiss.entity.UserGeolocationInfo;
import zx.vanx.user_permiss.service.UserGeolocationInfoService;

/**
 * <p>
 * 用户地理位置信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-13
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/user-geolocation-info")
public class UserGeolocationInfoController  {

    private final UserGeolocationInfoService userGeolocationInfoService;

    @ApiOperation("保存或修改用户地理位置信息")
    @PostMapping("/save-or-update-geolocation-info")
    
    public ResultData<Object> saveOrUpdateGeolocationInfo(@RequestBody UserGeolocationInfo userGeolocationInfo) {

        userGeolocationInfo.setUserId(LoginHelper.getUserId());
        userGeolocationInfoService.saveOrUpdateGeolocationInfo(userGeolocationInfo);
        return ResultData.ok().Message("保存或修改用户地理位置信息成功");
    }

}

