package zx.vanx.user_permiss.controller.frontend;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.service.MinioService;
import zx.vanx.user_permiss.entity.PlatfUserLevelInfo;
import zx.vanx.user_permiss.service.UserLevelInfoService;
import zx.vanx.user_permiss.vo.UserLevelSelectVo;
import zx.vanx.user_permiss.vo.VipCardLevelRightsVo;
import zx.vanx.vo.ImageData;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Api(tags = "c、用户-等级管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/user-level-info")
public class UserLevelInfoController {

    private final UserLevelInfoService userLevelInfoService;

    private final MinioService minioService;

    @ApiOperation("增加")
    @PostMapping("/save")
    public ResultData<Void> save(@RequestPart PlatfUserLevelInfo userLevelInfo , @RequestPart MultipartFile userLevelIconFile, HttpServletRequest request) {
        Long userId = LoginHelper.getUserId();
        userLevelInfo.setCreatorId(userId);
        ImageData imageData = minioService.uploadFile(userLevelIconFile);
        userLevelInfo.setUserLevelIcon(imageData.getUrl());
        userLevelInfo.setUserLevelIconMinioName(imageData.getFileName());
        userLevelInfoService.save(userLevelInfo);
        return ResultData.ok();
    }

    @ApiOperation("列表")
    @PostMapping("/list/{UserLevelType}")
    public ResultData<List<PlatfUserLevelInfo>> listByUserLevelType(@PathVariable(value = "UserLevelType") String UserLevelType) {

        List<PlatfUserLevelInfo> userLevelInfoList = userLevelInfoService.queryLevelAndRightsList(UserLevelType);

        return ResultData.ok(userLevelInfoList);
    }

    /**
     * 查询用户等级下拉列表
     * @param userLevelType 用户等级类型
     * @return 用户等级下拉列表
     */
    @ApiOperation("用户等级下拉列表")
    @PostMapping("/user-level-select")
    public ResultData<List<UserLevelSelectVo>> queryUserLevelSelectList(@RequestParam(value = "userLevelType") String userLevelType) {

        List<UserLevelSelectVo> userLevelInfoList = userLevelInfoService.queryUserLevelSelectList(userLevelType);

        return ResultData.ok(userLevelInfoList);
    }

    @ApiOperation("根据等级id查询等级信息")
    @PostMapping("/user-level-info")
    public ResultData<PlatfUserLevelInfo> queryUserLevelInfo(@RequestParam(value = "levelInfoId") Long levelInfoId) {

        PlatfUserLevelInfo platfUserLevelInfo = userLevelInfoService.queryUserLevelInfo(levelInfoId);

        return ResultData.ok(platfUserLevelInfo);
    }

    @ApiOperation("会员卡查询等级权益基本信息")
    @PostMapping("/vip-card-user-level-rights")
    public ResultData<VipCardLevelRightsVo> queryVipCardLevelRights(@RequestParam(value = "userBehaviorType",required = false) String userBehaviorType,
                                                                          @RequestParam(value = "userLevelId",required = false) Long userLevelId) {

        VipCardLevelRightsVo userLevelInfoList = userLevelInfoService.queryVipCardLevelRights(userBehaviorType, userLevelId);
        return ResultData.ok(userLevelInfoList);
    }

    @ApiOperation("修改")
    @PostMapping("/modify")
    public ResultData<Void> modify(@RequestBody PlatfUserLevelInfo userLevelInfo) {
        userLevelInfoService.updateById(userLevelInfo);
        return ResultData.ok();
    }

    @ApiOperation("删除")
    @PostMapping("/remove/{levelInfoId}")
    public ResultData<Void> removeLevelInfo(@PathVariable(value = "levelInfoId") Long levelInfoId) {
        userLevelInfoService.removeLevelInfo(levelInfoId);

        return ResultData.ok();
    }

}
