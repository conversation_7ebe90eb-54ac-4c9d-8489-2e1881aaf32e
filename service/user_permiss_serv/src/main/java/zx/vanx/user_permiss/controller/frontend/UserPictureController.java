package zx.vanx.user_permiss.controller.frontend;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.exception.ZxException;
import zx.vanx.common_util.utils.HTTPJsonUtil;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.user_permiss.entity.UserPicture;
import zx.vanx.user_permiss.service.UserPictureService;
import zx.vanx.user_permiss.vo.query.UserSelectedPictureVo;

import java.nio.charset.StandardCharsets;
import java.util.List;
@Api(tags = "h、用户-照片")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/user-picture")
public class UserPictureController {

    private final UserPictureService userPictureService;

    @ApiOperation(value = "精选照片-保存")
    @PostMapping("/save-user-selected-picture")
    public ResultData<Void>  saveUserSelectedPicture(@RequestParam(value = "files") MultipartFile[] files) {

        if (CollectionUtils.isEmpty(CollectionUtils.arrayToList(files))) {
            throw new RuntimeException("精选照片不能为空");
        }

        userPictureService.saveUserSelectedPicture(files);
        return ResultData.ok();
    }

    @ApiOperation(value = "精选照片-列表-查询")
    @PostMapping("/select-user-selected-picture")
    public ResultData<UserSelectedPictureVo> selectUserSelectedPicture() {
        UserSelectedPictureVo userSelectedPictureVo = userPictureService.selectUserSelectedPicture(LoginHelper.getUserId());
        return ResultData.ok(userSelectedPictureVo);
    }

    @ApiOperation(value = "精选照片-删除")
    @PostMapping("/delete-user-selected-picture")
    public ResultData<Void> deleteUserSelectedPicture(@RequestBody List<UserPicture> imagesList) {
        userPictureService.deleteUserSelectedPicture(imagesList);
        return ResultData.ok();
    }

    @ApiOperation(value = "个人头像-修改")
    @PostMapping("/update-user-avatar-picture")
    public ResultData<UserPicture> updateUserAvatarPicture(@RequestPart(value = "obj") String obj, @RequestPart(value = "file") MultipartFile file){

        // 使用Hutool的JSONUtil将字符串反序列化为UserCover对象
         UserPicture userAvatar = HTTPJsonUtil.parseObj(obj, UserPicture.class);
        if (ObjectUtils.isEmpty(userAvatar)) {
            throw new ZxException(201, "个人头像不能为空");
        }

        // 手动解码文件名
        String filename = new String(file.getOriginalFilename().getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
        System.out.println("Decoded filename: " + filename);
        UserPicture userPicture = userPictureService.updateUserPicture(userAvatar, file);

        return ResultData.ok(userPicture).Message("修改成功");
    }

    @ApiOperation(value = "个人封面-修改")
    @PostMapping("/update-user-cover-picture")
    public ResultData<UserPicture> updateUserCoverPicture(@RequestPart(value = "obj") String obj, @RequestPart(value = "file") MultipartFile file){

        // 使用Hutool的JSONUtil将字符串反序列化为UserCover对象
        UserPicture userCover = HTTPJsonUtil.parseObj(obj, UserPicture.class);

        if (ObjectUtils.isEmpty(userCover)) {
            throw new ZxException(201, "封面照片不能为空");
        }

        UserPicture userPicture = userPictureService.updateUserPicture(userCover, file);
        return ResultData.ok(userPicture).Message("修改成功");
    }

    @ApiOperation("查询用户个人头像")
    @PostMapping("/select-user-avatar-picture")
    public UserPicture selectUserAvatarPicture() {
        return userPictureService.selectUserAvatarPicture(LoginHelper.getUserId());
    }
}
