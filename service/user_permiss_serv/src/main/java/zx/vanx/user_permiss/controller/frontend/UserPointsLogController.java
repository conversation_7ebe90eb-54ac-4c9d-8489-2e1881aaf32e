package zx.vanx.user_permiss.controller.frontend;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.user_permiss.request.DynamicPointsRequest;
import zx.vanx.user_permiss.request.NoSystemPointsAddRequest;
import zx.vanx.user_permiss.request.SystemPointsAddRequest;
import zx.vanx.user_permiss.service.UserPointsAddLogService;
import zx.vanx.user_permiss.service.UserPointsLogService;
import zx.vanx.user_permiss.vo.PointsVo;

import javax.validation.Valid;

@Api(tags = "用户-积分日志记录")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/user-points-log")
public class UserPointsLogController  {

    private final UserPointsAddLogService userPointsAddLogService;

    private final UserPointsLogService userPointsLogService;

    /**
     * 系统配置行为积分-增益
     * @param systemPointsAddRequest 系统添加积分请求对象
     * @return 成功或失败
     */
    @ApiOperation("系统配置行为积分-增益")
    @PostMapping("/systemPointsGain")
    
    public ResultData<Void> systemPointsGain(@RequestBody SystemPointsAddRequest systemPointsAddRequest) {

        userPointsAddLogService.systemPointsGain(systemPointsAddRequest, LoginHelper.getUserId());
        return ResultData.ok(null);

    }

    /**
     * 非系统配置行为积分-增益
     * @param noSystemPointsAddRequest 非系统添加积分请求对象
     * @return 成功或失败
     */
    @ApiOperation("非系统配置行为积分-增益")
    
    @PostMapping("/noSystemPointsGain")
    public ResultData<Void> noSystemPointsGain(@RequestBody NoSystemPointsAddRequest noSystemPointsAddRequest) {

        noSystemPointsAddRequest.setUserId(LoginHelper.getUserId());
        userPointsAddLogService.noSystemPointsGain(noSystemPointsAddRequest,LoginHelper.getUserId());
        return ResultData.ok(null);
    }

    @ApiOperation("查询用户动态积分")
    
    @PostMapping("/user-dynamic-points")
    public ResultData<PointsVo> queryUserDynamicPoints(@RequestBody @Valid DynamicPointsRequest dynamicPointsRequest) {

//        if (ObjectUtils.isEmpty(dynamicPointsRequest.getTransactionType())) {
//            throw new ZxException(204,"交易类型不能为空");
//        }
//
//        if (ObjectUtils.isEmpty(dynamicPointsRequest.getDateOption())) {
//            throw new ZxException(204,"日期选项不能为空");
//        }

        PointsVo pointsVo = userPointsLogService.queryUserDynamicPoints(LoginHelper.getUserId(),dynamicPointsRequest);
        return ResultData.ok(pointsVo);
    }

}
