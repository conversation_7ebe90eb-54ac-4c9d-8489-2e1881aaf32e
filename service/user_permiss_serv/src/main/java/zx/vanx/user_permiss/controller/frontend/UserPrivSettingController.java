package zx.vanx.user_permiss.controller.frontend;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.user_permiss.entity.UserPrivSetting;
import zx.vanx.user_permiss.service.UserPrivSettingService;
import zx.vanx.user_permiss.vo.AppPassword;
import zx.vanx.user_permiss.vo.loginAppInfo;


@Api(tags = "用户-隐私设置")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/user-priv-setting")
public class UserPrivSettingController {

    private final UserPrivSettingService userPrivSettingService;

    @ApiOperation(value = "用户是否开启隐藏功能")
    @PostMapping("/user-is-open-hide-function")
    public ResultData<Boolean> userIsOpenHideFunction() {
        Boolean isHide = userPrivSettingService.userIsOpenHideFunction(LoginHelper.getUserId());
        return ResultData.ok(isHide).Message("查询成功");
    }

    @ApiOperation(value = "当前用户是否设置启动时加密，若设置，密码类型是啥 加密方式")
    @PostMapping("/user-is-setting-encrypt-method/{encryptMethod}")
    public ResultData<AppPassword> userIsSettingEncryptMethod(@PathVariable(value = "encryptMethod") String encryptMethod) {

        AppPassword appPassword = userPrivSettingService.userIsSettingEncryptMethod(LoginHelper.getUserId(),encryptMethod);
        return ResultData.ok(appPassword).Message("查询成功");

    }

    @ApiOperation(value = "App用户登录")
    @PostMapping("/login-app")
    public ResultData<Boolean> loginApp(@RequestBody loginAppInfo loginAppInfo) {

        Boolean result = userPrivSettingService.loginApp(loginAppInfo);
        return ResultData.ok(result).Message("登录成功");

    }

    @ApiOperation(value = "修改用户的隐私设置")
    @PostMapping("/save-or-update-setting")
    public ResultData<Boolean> saveOrUpdateSetting(@RequestBody UserPrivSetting userPrivSetting) {
        boolean result = userPrivSettingService.saveOrUpdate(userPrivSetting);
        return ResultData.ok(result).Message("修改成功");
    }
}