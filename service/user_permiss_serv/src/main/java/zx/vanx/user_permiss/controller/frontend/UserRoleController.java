package zx.vanx.user_permiss.controller.frontend;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import zx.vanx.user_permiss.dto.AddUserRoleDto;
import zx.vanx.user_permiss.service.PlatfUserRolesService;

@Api(tags = "用户角色管理")
@RequiredArgsConstructor
@RestController("/user_permiss/user-role")
public class UserRoleController {

    private final PlatfUserRolesService platfUserRolesService;


    @ApiOperation("添加用户角色身份")
    @PostMapping("/add-user-role-identity")
    public boolean addUserRoleIdentity(@RequestBody AddUserRoleDto addUserRoleDto) {

        return platfUserRolesService.addUserRoleIdentity(addUserRoleDto);
    }

}
