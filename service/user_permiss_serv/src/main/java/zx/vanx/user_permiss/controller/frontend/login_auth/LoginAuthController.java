package zx.vanx.user_permiss.controller.frontend.login_auth;

import com.alibaba.fastjson2.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.user_permiss.dto.LoginResponseDTO;
import zx.vanx.user_permiss.dto.request.BindPhoneRequest;
import zx.vanx.user_permiss.dto.request.PasswordLoginRequest;
import zx.vanx.user_permiss.dto.request.PhoneLoginRequest;
import zx.vanx.user_permiss.dto.request.QQLoginRequest;
import zx.vanx.user_permiss.dto.request.SetPasswordRequest;
import zx.vanx.user_permiss.service.LoginAuthService;
import zx.vanx.justauth.service.JustAuthService;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthUser;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/auth")
@Api(tags = "第三方登录接口")
public class LoginAuthController {

    private final LoginAuthService loginAuthService;
    private final JustAuthService justAuthService;

    /**
     * 处理移动APP的QQ登录
     *
     * @param request QQ登录请求
     * @return 登录结果
     */
    @ApiOperation(value = "移动APP QQ登录", notes = "处理移动APP从腾讯QQ SDK获取的授权信息")
    @PostMapping("/app/login/qq")
    public ResultData<LoginResponseDTO> qqLogin(
            @ApiParam(value = "QQ登录请求", required = true) @Valid @RequestBody QQLoginRequest request) {

        log.info("移动APP QQ登录参数：{}", request);

        log.info("移动APP QQ登录参数：{}", JSON.toJSONString(request));

        // 处理QQ登录
        LoginResponseDTO response = loginAuthService.handleOAuthLogin("qq", request.getOpenid(),
                request.getAccessToken(), request.getPayToken(), request.getExpiresIn(), JSON.toJSONString(request));

        return ResultData.ok(response);
    }

    /**
     * 手机号一键登录
     *
     * @param request 手机号登录请求
     * @return 登录结果
     */
    @ApiOperation(value = "手机号一键登录", notes = "使用运营商SDK获取的手机号进行一键登录")
    @PostMapping("/phone/login")
    public ResultData<LoginResponseDTO> phoneLogin(
            @ApiParam(value = "手机号登录请求", required = true) @Valid @RequestBody PhoneLoginRequest request) {

        log.info("手机号一键登录参数：{}", request);

        // 处理手机号登录
        LoginResponseDTO response = loginAuthService.phoneLogin(request.getPhone());

        return ResultData.ok(response);
    }

    /**
     * 使用短信验证码进行手机号登录
     *
     * @param phone   手机号
     * @param smsCode 短信验证码
     * @return 登录结果
     */
    @ApiOperation(value = "手机号验证码登录", notes = "使用短信验证码进行登录")
    @PostMapping("/sms/login")
    public ResultData<LoginResponseDTO> smsCodeLogin(
            @ApiParam(value = "手机号", required = true) @RequestParam("phone") @NotBlank(message = "手机号不能为空") @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确") String phone,

            @ApiParam(value = "短信验证码", required = true) @RequestParam("smsCode") @NotBlank(message = "验证码不能为空") @Pattern(regexp = "^\\d{6}$", message = "验证码格式不正确") String smsCode) {

        log.info("短信验证码登录参数：phone={}, smsCode={}", phone, smsCode);

        // 处理验证码登录
        LoginResponseDTO response = loginAuthService.smsCodeLogin(phone, smsCode);

        return ResultData.ok(response);
    }

    /**
     * 手机号密码登录
     *
     * @param request 密码登录请求
     * @return 登录结果
     */
    @ApiOperation(value = "手机号密码登录", notes = "使用手机号和密码进行登录")
    @PostMapping("/password/login")
    public ResultData<LoginResponseDTO> passwordLogin(
            @ApiParam(value = "密码登录请求", required = true) @Valid @RequestBody PasswordLoginRequest request) {

        log.info("手机号密码登录参数：phone={}", request.getPhone());

        // 处理密码登录
        LoginResponseDTO response = loginAuthService.passwordLogin(request.getPhone(), request.getPassword());

        return ResultData.ok(response);
    }

    /**
     * 设置登录密码
     *
     * @param request 设置密码请求
     * @return 操作结果
     */
    @ApiOperation(value = "设置登录密码", notes = "通过短信验证码为用户设置登录密码")
    @PostMapping("/password/set")
    public ResultData<Boolean> setPassword(
            @ApiParam(value = "设置密码请求", required = true) @Valid @RequestBody SetPasswordRequest request) {

        log.info("设置密码参数：phone={}", request.getPhone());

        // 处理设置密码
        Boolean result = loginAuthService.setPassword(request.getPhone(), request.getSmsCode(), request.getPassword());

        return ResultData.ok(result);
    }

    /**
     * 第三方登录绑定手机号
     *
     * @param request 绑定手机号请求
     * @return 登录结果
     */
    @ApiOperation(value = "第三方登录绑定手机号", notes = "用于第三方登录首次使用时绑定手机号")
    @PostMapping("/bind/phone")
    public ResultData<LoginResponseDTO> bindPhone(
            @ApiParam(value = "绑定手机号请求", required = true) @Valid @RequestBody BindPhoneRequest request) {

        log.info("绑定手机号参数：{}", request);

        // 处理绑定手机号
        LoginResponseDTO response = loginAuthService.bindPhone(request.getIdentityType(), request.getTempToken(),
                request.getPhone(), request.getSmsCode());

        return ResultData.ok(response);
    }

    /**
     * 获取Web端QQ登录授权链接
     *
     * @param state 自定义状态参数（可选，用于防CSRF攻击）
     * @return QQ授权链接
     */
    @ApiOperation(value = "获取Web端QQ登录授权链接", notes = "获取QQ扫码登录或一键授权的链接地址")
    @GetMapping("/web/authorize/qq")
    public ResultData<String> getQQAuthorizeUrl(
            @ApiParam(value = "状态参数", required = false) @RequestParam(value = "state", required = false) String state) {

        log.info("获取QQ授权链接，state={}", state);

        try {
            // 使用JustAuth获取QQ授权链接
            String authorizeUrl = justAuthService.getAuthorizeUrl("QQ", state);
            
            log.info("QQ授权链接生成成功：{}", authorizeUrl);
            
            return ResultData.ok(authorizeUrl);
        } catch (Exception e) {
            log.error("获取QQ授权链接失败", e);
            return ResultData.error("获取授权链接失败：" + e.getMessage());
        }
    }

    /**
     * Web端QQ登录回调处理
     *
     * @param code  授权码
     * @param state 状态参数
     * @return 登录结果
     */
    @ApiOperation(value = "Web端QQ登录回调", notes = "处理QQ登录授权回调，完成登录流程")
    @GetMapping("/web/callback/qq")
    public ResultData<LoginResponseDTO> qqWebCallback(
            @ApiParam(value = "授权码", required = true) @RequestParam("code") String code,
            @ApiParam(value = "状态参数", required = false) @RequestParam(value = "state", required = false) String state) {

        log.info("QQ Web端登录回调，code={}, state={}", code, state);

        try {
            // 构建回调参数
            AuthCallback authCallback = AuthCallback.builder()
                    .code(code)
                    .state(state)
                    .build();

            // 使用JustAuth处理Web登录
            AuthResponse<AuthUser> authResponse = justAuthService.handleWebLogin("QQ", authCallback);

            if (authResponse.getCode() == 2000) {
                AuthUser authUser = authResponse.getData();
                
                log.info("QQ Web登录授权成功，用户信息：openid={}, nickname={}", 
                        authUser.getUuid(), authUser.getNickname());

                // 处理登录逻辑，复用现有的handleOAuthLogin方法
                LoginResponseDTO response = loginAuthService.handleOAuthLogin(
                        "qq", 
                        authUser.getUuid(),           // openid
                        authUser.getToken().getAccessToken(),  // accessToken
                        authUser.getToken().getRefreshToken(), // refreshToken
                        String.valueOf(authUser.getToken().getExpireIn()), // expiresIn
                        JSON.toJSONString(authUser)   // 完整用户信息
                );

                return ResultData.ok(response);
            } else {
                log.error("QQ Web登录授权失败：{}", authResponse.getMsg());
                return ResultData.error("QQ登录失败：" + authResponse.getMsg());
            }
        } catch (Exception e) {
            log.error("QQ Web登录回调处理失败", e);
            return ResultData.error("登录失败：" + e.getMessage());
        }
    }
}
