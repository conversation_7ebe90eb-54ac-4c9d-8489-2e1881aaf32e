package zx.vanx.user_permiss.controller.frontend.pc_internal_video;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.user_permiss.service.UserService;
import zx.vanx.user_permiss.vo.UpdateUserBaseInfo;

@Api(tags = "pc端-用户基本信息")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/pc_internal-video/user-base-info")
public class UserBaseInfoController {

    private final UserService userService;

    @ApiOperation("修改-用户基本信息")
    @PostMapping("/update")
    public ResultData<Object> updateUserBaseInfo(@RequestBody UpdateUserBaseInfo updateUserBaseInfo) {

        userService.updateUserBaseInfo(updateUserBaseInfo);
        return ResultData.ok().Message("修改成功");
    }

}
