import axios from 'axios';

/**
 * 上传单个文件和对象
 * @param url 上传地址
 * @param file1 文件
 * @param file2 文件
 * @param obj 对象
 */
export default function uploadFileAndObj(url, file1,file2, obj) {
    let formData = new FormData();
        formData.append('file', file1);
        formData.append('fil2', file2);
        formData.append('obj', encodeURIComponent(JSON.stringify(obj)));
    axios.post(url, formData, {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}



/**
 * 上传多个单文件和对象
 * @param url 上传地址
 * @param file1 文件1
 * @param file2 文件2
 * @param obj 对象
 */
export default function uploadFileAndObj(url, file1,file2, obj) {
    let formData = new FormData();
    formData.append('file1', file1);
    formData.append('file2', file2);
    for (let key in obj) {
        formData.append(key, obj[key]);
    }
    axios.post(url, formData, {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}

/**
 * 上传多个单文件
 * @param url 上传地址
 * @param file1 文件1
 * @param file2 文件2
 * @param obj 对象
 */
export default function uploadFiles(url, file1,file2) {
    let formData = new FormData();
    formData.append('IDCardFrontFile', file1);
    formData.append('IDCardReverseFile', file2);
    axios.post(url, formData, {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}

/**
 * 上传多个文件
 * @param url 上传地址
 * @param files 文件数组
 */
export default function uploadFiles(url, files) {
    let formData = new FormData();
    for (let i = 0; i < files.length; i++) {
        formData.append('files', files[i]);
    }
    axios.post(url, formData, {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}

/**
 * 上传多个文件和对象
 * @param url 上传地址
 * @param files 文件数组
 * @param obj 对象
 */
export default function uploadFilesAndObj(url, files, obj) {

    let formData = new FormData();

    for (let i = 0; i < files.length; i++) {
        formData.append('files', files[i]);
    }

    for (let key in obj) {
        formData.append(key, obj[key]);
    }

    axios.post(url, formData, {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}