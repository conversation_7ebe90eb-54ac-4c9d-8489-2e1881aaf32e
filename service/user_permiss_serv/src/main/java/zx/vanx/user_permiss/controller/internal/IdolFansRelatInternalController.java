package zx.vanx.user_permiss.controller.internal;


import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import zx.vanx.common_util.utils.ObjectUtil;
import zx.vanx.user_permiss.entity.IdolFansRelat;
import zx.vanx.user_permiss.service.IdolFansRelatService;
import zx.vanx.user_permiss.vo.UserAttentionPersonVo;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/idol-fans-relat-internal")
public class IdolFansRelatInternalController {

    private final IdolFansRelatService idolFansRelatService;

    @ApiOperation("查询当前用户是否为某作品用户粉丝")
    @PostMapping("/queryUserIsFollower")
    public boolean queryUserIsFollower(@RequestParam(value = "currentUserId") Long currentUserId,@RequestParam(value = "targetUserId") Long targetUserId) {

        IdolFansRelat idolFansRelat = idolFansRelatService.selectUserFollower(targetUserId, currentUserId);
        return !ObjectUtil.isEmpty(idolFansRelat);
    }

    @ApiOperation("获取用户关注的用户列表")
    @PostMapping("/getAttentionUserList")
    public List<UserAttentionPersonVo> getAttentionUserList(@RequestParam(value = "userId") Long userId) {
        return idolFansRelatService.queryUserAttentionPersonList(userId);
    }

    @ApiOperation("获取用户关注数")
    @PostMapping("/getUserFollowingCount")
    public Long getUserFollowingCount(@RequestParam(value = "userId") Long userId) {
        return idolFansRelatService.getUserFollowingCount(userId);
    }

    @ApiOperation("获取用户粉丝数")
    @PostMapping("/getUserFollowerCount")
    public Long getUserFollowerCount(@RequestParam(value = "userId") Long userId) {
        return idolFansRelatService.getUserFollowerCount(userId);
    }
}
