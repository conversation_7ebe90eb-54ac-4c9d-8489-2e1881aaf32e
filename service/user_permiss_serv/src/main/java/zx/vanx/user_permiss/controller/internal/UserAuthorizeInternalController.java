package zx.vanx.user_permiss.controller.internal;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.user_permiss.dto.UserAuthorizeDTO;
import zx.vanx.user_permiss.service.UserAuthorizeService;

import java.util.List;

/**
 * 用户授权信息内部接口Controller
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/user-authorize")
@Api(tags = "用户授权内部接口")
@ApiIgnore
public class UserAuthorizeInternalController {

    private final UserAuthorizeService userAuthorizeService;


    /**
     * 保存用户授权信息
     */
    @ApiOperation("保存用户授权信息")
    @PostMapping("/save")
    public ResultData<Boolean> saveAuthorize(@RequestBody UserAuthorizeDTO userAuthorizeDTO) {
        boolean result = userAuthorizeService.saveAuthorize(userAuthorizeDTO);
        return ResultData.ok(result);
    }

    /**
     * 更新用户授权信息
     */
    @ApiOperation("更新用户授权信息")
    @PutMapping("/update")
    public ResultData<Boolean> updateAuthorize(@RequestBody UserAuthorizeDTO userAuthorizeDTO) {
        boolean result = userAuthorizeService.updateAuthorize(userAuthorizeDTO);
        return ResultData.ok(result);
    }

    /**
     * 根据用户ID和身份类型查询授权信息
     */
    @ApiOperation("根据用户ID和身份类型查询授权信息")
    @GetMapping("/findByUserIdAndType")
    public ResultData<UserAuthorizeDTO> findByUserIdAndType(@RequestParam("userId") Long userId,
            @RequestParam("identityType") String identityType) {
        UserAuthorizeDTO userAuthorizeDTO = userAuthorizeService.findByUserIdAndType(userId, identityType);
        return ResultData.ok(userAuthorizeDTO);
    }

    /**
     * 根据用户ID查询所有授权信息
     */
    @ApiOperation("根据用户ID查询所有授权信息")
    @GetMapping("/findByUserId")
    public ResultData<List<UserAuthorizeDTO>> findByUserId(@RequestParam("userId") Long userId) {
        List<UserAuthorizeDTO> userAuthorizeDTOs = userAuthorizeService.findByUserId(userId);
        return ResultData.ok(userAuthorizeDTOs);
    }
}