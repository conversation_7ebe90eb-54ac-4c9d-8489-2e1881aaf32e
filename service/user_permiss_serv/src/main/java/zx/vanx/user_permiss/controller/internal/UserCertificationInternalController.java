package zx.vanx.user_permiss.controller.internal;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.user_permiss.service.UserService;
import zx.vanx.user_permiss.vo.PersonalAuthInfoVo;

/**
 * 用户认证内部接口控制器
 * 提供给其他微服务调用的认证相关接口
 * 
 * <AUTHOR>
 * @since 2025-07-04
 */
@Api(tags = "用户认证内部接口")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/internal/certification")
public class UserCertificationInternalController {

    private final UserService userService;

    @ApiOperation("查询用户个人实名认证状态")
    @GetMapping("/personal-auth-status/{userId}")
    public ResultData<PersonalAuthInfoVo> getUserPersonalAuthStatus(@PathVariable("userId") Long userId) {
        
        // 调用Service层查询用户个人认证状态
        PersonalAuthInfoVo personalAuthInfo = userService.queryUserIsPersonalAuth(userId);
        
        return ResultData.ok(personalAuthInfo).Message("用户个人认证状态查询成功");
    }
}