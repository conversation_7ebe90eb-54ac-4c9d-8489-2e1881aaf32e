package zx.vanx.user_permiss.controller.internal;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.user_permiss.dto.UserInfoDTO;
import zx.vanx.user_permiss.service.UserInfoService;

/**
 * 用户信息内部接口Controller
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/user_permiss/user-info")
@Api(tags = "用户信息内部接口")
@ApiIgnore
public class UserInfoInternalController {

    private final UserInfoService userInfoService;

    /**
     * 根据用户ID查询用户信息
     */
    @ApiOperation("根据用户ID查询用户信息")
    @GetMapping("/{id}")
    public ResultData<UserInfoDTO> getUserById(@PathVariable("id") Long id) {
        UserInfoDTO userInfoDTO = userInfoService.getUserById(id);
        return ResultData.ok(userInfoDTO);
    }

    /**
     * 保存用户信息
     */
    @ApiOperation("保存用户信息")
    @PostMapping("/save")
    public ResultData<UserInfoDTO> saveUser(@RequestBody UserInfoDTO userInfoDTO) {
        UserInfoDTO result = userInfoService.saveUser(userInfoDTO);
        return ResultData.ok(result);
    }

    /**
     * 更新用户信息
     */
    @ApiOperation("更新用户信息")
    @PutMapping("/update")
    public ResultData<Boolean> updateUser(@RequestBody UserInfoDTO userInfoDTO) {
        boolean result = userInfoService.updateUser(userInfoDTO);
        return ResultData.ok(result);
    }

    /**
     * 根据手机号查询用户信息
     */
    @ApiOperation("根据手机号查询用户信息")
    @GetMapping("/findByPhone")
    public ResultData<UserInfoDTO> findByPhone(@RequestParam("phone") String phone) {
        UserInfoDTO userInfoDTO = userInfoService.findByPhone(phone);
        return ResultData.ok(userInfoDTO);
    }
}