package zx.vanx.user_permiss.controller.internal;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import zx.vanx.user_permiss.service.UserService;
import zx.vanx.user_permiss.vo.UserDetail;

/**
 * <p>
 * 用户信息 内部服务接口
 * 提供用户详情查询等内部服务调用功能
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Api(tags = {"用户内部服务接口"})
@RestController
@RequestMapping("/user_permiss/user-internal")
@RequiredArgsConstructor
@Validated
@Slf4j
public class UserInternalController {

    private final UserService userService;

    @ApiOperation("根据用户ID获取用户详情")
    @PostMapping("/getUserDetailByUserId")
    public UserDetail getUserDetailByUserId(@RequestParam("userId") Long userId) {
        log.debug("内部服务调用：获取用户详情，用户ID: {}", userId);
        
        if (userId == null) {
            log.warn("获取用户详情失败：用户ID为空");
            return null;
        }
        
        try {
            return userService.getUserDetail(userId);
        } catch (Exception e) {
            log.error("获取用户详情失败，用户ID: {}, 错误信息: {}", userId, e.getMessage(), e);
            return null;
        }
    }

}