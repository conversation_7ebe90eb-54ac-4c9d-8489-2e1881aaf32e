package zx.vanx.user_permiss.controller.internal;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import zx.vanx.common_util.utils.BeanUtil;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.user_permiss.request.NoSystemPointsAddRequest;
import zx.vanx.user_permiss.request.NoSystemPointsReduceRequest;
import zx.vanx.user_permiss.request.SystemPointsAddRequest;
import zx.vanx.user_permiss.request_dto.NoSystemPointsAddRequestDTO;
import zx.vanx.user_permiss.request_dto.NoSystemPointsSubRequestDTO;
import zx.vanx.user_permiss.request_dto.SystemPointsAddRequestDTO;
import zx.vanx.user_permiss.service.UserPointsAddLogService;
import zx.vanx.user_permiss.service.UserPointsSubLogService;

@RestController
@Api(tags = "系统积分日志内部接口")
@RequiredArgsConstructor
@RequestMapping("/user_permiss/internal/user-points-log")
public class UserPointsLogInternalController {

    private final UserPointsAddLogService userPointsAddLogService;

    private final UserPointsSubLogService userPointsSubLogService;

    @ApiOperation("系统配置行为积分-增益")
    @PostMapping("/systemPointsGain")
    public ResultData<Void> systemPointsGain(@RequestBody SystemPointsAddRequestDTO systemPointsAddRequestDTO) {

        SystemPointsAddRequest systemPointsAddRequest = BeanUtil.copyProperties(systemPointsAddRequestDTO, SystemPointsAddRequest::new);

        Long userId = systemPointsAddRequest.getUserId();
        if (ObjectUtils.isEmpty(userId)) {
            return ResultData.error("用户ID不能为空");
        }
        userPointsAddLogService.systemPointsGain(systemPointsAddRequest,systemPointsAddRequest.getUserId());
        return ResultData.ok(null);
    }

    @ApiOperation("非系统配置行为积分-增益")
    @PostMapping("/noSystemPointsGain")
    public ResultData<Void> noSystemPointsGain(@RequestBody NoSystemPointsAddRequestDTO noSystemPointsAddRequestDTO) {

        NoSystemPointsAddRequest noSystemPointsAddRequest = BeanUtil.copyProperties(noSystemPointsAddRequestDTO, NoSystemPointsAddRequest::new);

        Long userId = noSystemPointsAddRequest.getUserId();
        if (ObjectUtils.isEmpty(userId)) {
            return ResultData.error("用户ID不能为空");
        }

        userPointsAddLogService.noSystemPointsGain(noSystemPointsAddRequest,noSystemPointsAddRequest.getUserId());
        return ResultData.ok(null);
    }

    @ApiOperation("非系统配置行为积分-减少")
    @PostMapping("/noSystemPointsReduce")
    public ResultData<Void> noSystemPointsReduce(@RequestBody NoSystemPointsSubRequestDTO noSystemPointsSubRequestDTO) {

        NoSystemPointsReduceRequest noSystemPointsReduceRequest = BeanUtil.copyProperties(noSystemPointsSubRequestDTO, NoSystemPointsReduceRequest::new);

        Long userId = noSystemPointsReduceRequest.getUserId();
        if (ObjectUtils.isEmpty(userId)) {
            return ResultData.error("用户ID不能为空");
        }

        userPointsSubLogService.noSystemPointsReduce(noSystemPointsReduceRequest,noSystemPointsReduceRequest.getUserId());
        return ResultData.ok(null);
    }
}
