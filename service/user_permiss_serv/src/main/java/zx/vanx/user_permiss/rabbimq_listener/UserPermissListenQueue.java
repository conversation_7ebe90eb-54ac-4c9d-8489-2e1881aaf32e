package zx.vanx.user_permiss.rabbimq_listener;

import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import zx.vanx.aspect.Idempotent;
import zx.vanx.common_util.utils.BeanUtil;
import zx.vanx.constant.MqConst;
import zx.vanx.user_permiss.entity.InboxTimeline;
import zx.vanx.user_permiss.entity.UserInfo;
import zx.vanx.user_permiss.rabbit.entity.RabbitInboxTimeline;
import zx.vanx.user_permiss.service.FriendInfoService;
import zx.vanx.user_permiss.service.IdolFansRelatService;
import zx.vanx.user_permiss.service.InboxTimelineService;
import zx.vanx.user_permiss.vo.UserFollowersVo;

import java.util.List;

/**
 * author
 * Date: 2023/9/1315:42
 **/
@Component
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class UserPermissListenQueue {

    private final IdolFansRelatService idolFansRelatService;

    private final InboxTimelineService inboxTimelineService;

    private final FriendInfoService friendInfoService;

    /**
     * 监听时间线表的队列业务
     */
    @SneakyThrows
    @RabbitListener(bindings = @QueueBinding(
            exchange = @Exchange( value = MqConst.EXCHANGE_DIRECT_TIMELINE_RECORD,autoDelete = "false"),
            key = {MqConst.ROUTING_TIMELINE_RECORD},
            value = @Queue( value = MqConst.QUEUE_TIMELINE_RECORD,durable = "true",autoDelete = "false"))
    )
    @Idempotent
    public void process(RabbitInboxTimeline rabbitInboxTimeline, Message message, Channel channel) {

        try {
            switch (rabbitInboxTimeline.getRuleName()) {
                case "粉丝":
                    // 查询该用户粉丝
                    List<UserFollowersVo> userFollowersVoList = idolFansRelatService.queryUserFollowerList(rabbitInboxTimeline.getPublishUserId());
                    // 为粉丝添加时间线表
                    for (UserFollowersVo userFollowersVo : userFollowersVoList) {
                        InboxTimeline inboxTimeline = BeanUtil.copyProperties(rabbitInboxTimeline, InboxTimeline::new);
                        inboxTimeline.setReceiveUserId(userFollowersVo.getUserFollowersId());
                        inboxTimelineService.save(inboxTimeline);
                    }
                    break;
                case "好友圈":
                    // 查询该用户好友
                    List<UserInfo> friendUserInfoList = friendInfoService.queryFriendInfoList(rabbitInboxTimeline.getPublishUserId());
                    // 为好友添加时间线表
                    for (UserInfo userinfo : friendUserInfoList) {
                        InboxTimeline inboxTimeline = BeanUtil.copyProperties(rabbitInboxTimeline, InboxTimeline::new);
                        inboxTimeline.setReceiveUserId(userinfo.getUserId());
                        inboxTimelineService.save(inboxTimeline);
                    }
                    break;
                case "指定好友可见":
                    // 获取该用户指定好友
                    Long[] friendIds = rabbitInboxTimeline.getFriendIds();
                    // 为好友添加时间线表
                    for (Long friendId : friendIds) {
                        InboxTimeline inboxTimeline = BeanUtil.copyProperties(rabbitInboxTimeline, InboxTimeline::new);
                        inboxTimeline.setReceiveUserId(friendId);
                        inboxTimelineService.save(inboxTimeline);
                    }
                    break;
                default:
                    System.out.println("消费者消息内容：" + rabbitInboxTimeline);
            }

            // Acknowledge the message
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            // If there's an error, reject the message
            channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
        }
    }


}
