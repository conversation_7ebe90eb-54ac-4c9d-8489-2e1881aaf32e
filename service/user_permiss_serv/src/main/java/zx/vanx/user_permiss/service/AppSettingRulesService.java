package zx.vanx.user_permiss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import zx.vanx.user_permiss.entity.AppSettingRules;

import java.util.List;

/**
 * <p>
 * 平台系统设置规则表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-10
 */
public interface AppSettingRulesService extends IService<AppSettingRules> {

    /**
     * 查询平台系统设置规则表列表
     * @param appRuleId 父级id
     * @return 平台规则列表
     */
    AppSettingRules selectAppSettingRulesOne(Long appRuleId);

    /**
     * 查询-顶层设置列表
     * @param parentAppRuleId 父级id
     * @return 平台规则列表
     */
    List<AppSettingRules> selectAppSettingRulesList(Long parentAppRuleId);
}
