package zx.vanx.user_permiss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import zx.vanx.user_permiss.entity.FriendInfo;
import zx.vanx.user_permiss.entity.UserInfo;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
public interface FriendInfoService extends IService<FriendInfo> {
    /**
     * 远程通过userId查询用户的好友列表
     * @param userId 用户id
     * @return 好友列表
     */
    List<UserInfo> queryFriendInfoList(Long userId);

    /**
     * 查询当前用户是否为某作品用户互关朋友
     * @param mediaUserId 作品用户id
     * @return 是否互关
     */
    boolean queryUserTogetherFriends(Long mediaUserId);

    /**
     * 关注添加好友
     * @param userId 用户id
     * @param idolId 偶像id
     * @return 结果
     */
    boolean insertFriendByFollow(Long userId, Long idolId);

}
