package zx.vanx.user_permiss.service;

import zx.vanx.user_permiss.entity.GrowthValueWeight;
import com.baomidou.mybatisplus.extension.service.IService;
import zx.vanx.user_permiss.entity.UserInfoFast;

/**
 * <p>
 * vanx_growth_value_weight 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
public interface GrowthValueWeightService extends IService<GrowthValueWeight> {

    /**
     * 通过成长值权重增加用户成长值
     * @param userInfoFast 用户信息
     */
    UserInfoFast addUserGrowthValueByGrowthValueWeight(UserInfoFast userInfoFast);

}
