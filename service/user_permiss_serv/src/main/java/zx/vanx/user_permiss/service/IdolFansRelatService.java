package zx.vanx.user_permiss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import zx.vanx.common_util.core.page.PageObj;
import zx.vanx.common_util.core.page.QueryPageDto;
import zx.vanx.user_permiss.entity.IdolFansRelat;
import zx.vanx.user_permiss.vo.AttentionData;
import zx.vanx.user_permiss.vo.UserAttentionPersonVo;
import zx.vanx.user_permiss.vo.UserFollowersVo;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-25
 */
public interface IdolFansRelatService extends IService<IdolFansRelat> {

    /**
     * 关注用户
     * @param userId 用户id
     * @param idolId 偶像id
     */
    void attentionUser(Long userId, Long idolId);

    /**
     * 查询用户粉丝列表
     * @param userId 用户id
     * @return 粉丝列表
     */
    List<UserFollowersVo> queryUserFollowerList(Long userId);

    /**
     * 查询用户粉丝列表（分页）
     * @param userId 用户id
     * @param queryPageDto 分页参数
     * @return 粉丝列表
     */
    PageObj<UserFollowersVo> queryUserFollowerListWithPage(Long userId, QueryPageDto queryPageDto);

    /**
     * 查询用户关注列表
     * @param userId 用户id
     * @return 关注列表
     */
    List<UserAttentionPersonVo> queryUserAttentionPersonList(Long userId);

    /**
     * 查询用户关注列表（分页）
     * @param userId 用户id
     * @param queryPageDto 分页参数
     * @return 关注列表
     */
    PageObj<UserAttentionPersonVo> queryUserAttentionPersonListWithPage(Long userId, QueryPageDto queryPageDto);

    /**
     * 查询用户粉丝
     * @param userId 用户id
     * @param followerId 粉丝id
     * @return
     */
    IdolFansRelat selectUserFollower(Long userId, Long followerId);

    /**
     * 查询用户关注下的视频 | 图片 | 微博 | 笔记
     * @param userId 用户id
     * @param fileType 文件类型
     * @return 数据
     */
    AttentionData queryUserAttentionList(Long userId, String fileType);

    /**
     * 取关用户
     * @param userId 用户id
     * @param idolId 偶像id
     */
    void userCancelAttention(Long userId, Long idolId);

    /**
     * 获取用户关注数
     * @param userId 用户id
     * @return 关注数
     */
    Long getUserFollowingCount(Long userId);

    /**
     * 获取用户粉丝数
     * @param userId 用户id
     * @return 粉丝数
     */
    Long getUserFollowerCount(Long userId);
}
