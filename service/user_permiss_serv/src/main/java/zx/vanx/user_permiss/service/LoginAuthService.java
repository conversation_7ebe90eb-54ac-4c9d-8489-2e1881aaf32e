package zx.vanx.user_permiss.service;

import zx.vanx.user_permiss.dto.LoginResponseDTO;
import zx.vanx.user_permiss.dto.UserAuthorizeDTO;
import zx.vanx.user_permiss.entity.UserAuthorize;

import java.util.List;

public interface LoginAuthService {
    /**
     * 根据身份类型和标识符查询授权信息
     */
    UserAuthorize findByTypeAndIdentifier(String identityType, String identifier);

    UserAuthorize findByTypeAndUserId(String identityType, Long userId);

    /**
     * 保存用户授权信息
     */
    boolean saveAuthorize(UserAuthorizeDTO userAuthorizeDTO);

    /**
     * 更新用户授权信息
     */
    boolean updateAuthorize(UserAuthorizeDTO userAuthorizeDTO);

    /**
     * 根据用户ID和身份类型查询授权信息
     */
    UserAuthorizeDTO findByUserIdAndType(Long userId, String identityType);

    /**
     * 根据用户ID查询所有授权信息
     */
    List<UserAuthorizeDTO> findByUserId(Long userId);

    /**
     * 处理OAuth第三方登录
     *
     * @param identityType 身份类型
     * @param identifier   标识符
     * @param accessToken  访问令牌
     * @param payToken     支付令牌
     * @param expiresIn    过期时间
     * @param oauthInfo    OAuth原始信息
     * @return 登录响应
     */
    LoginResponseDTO handleOAuthLogin(String identityType, String identifier, String accessToken, String payToken,
                                      String expiresIn, String oauthInfo);

    /**
     * 手机号一键登录
     *
     * @param phone 手机号
     * @return 登录响应
     */
    LoginResponseDTO phoneLogin(String phone);

    /**
     * 短信验证码登录
     *
     * @param phone   手机号
     * @param smsCode 短信验证码
     * @return 登录响应
     */
    LoginResponseDTO smsCodeLogin(String phone, String smsCode);

    /**
     * 绑定手机号
     *
     * @param identityType 身份类型
     * @param tempToken    临时令牌
     * @param phone        手机号
     * @param smsCode      短信验证码
     * @return 登录响应
     */
    LoginResponseDTO bindPhone(String identityType, String tempToken, String phone, String smsCode);

    /**
     * 手机号密码登录
     *
     * @param phone    手机号
     * @param password 密码
     * @return 登录响应
     */
    LoginResponseDTO passwordLogin(String phone, String password);

    /**
     * 设置登录密码
     *
     * @param phone    手机号
     * @param smsCode  短信验证码
     * @param password 密码
     * @return 操作结果
     */
    Boolean setPassword(String phone, String smsCode, String password);
}
