package zx.vanx.user_permiss.service;


import zx.vanx.common_util.utils.ResultData;

/**
 * author
 * Date: 2023/3/2815:28
 **/
public interface MsmService {

    /**
     * 发送登录验证码
     * @param phone 接受验证码手机号
     */
    ResultData<String> sendLoginCode(String phone);

    /**
     * 发送注册验证码
     * @param phone 接受验证码手机号
     */
    ResultData<String> sendRegisterCode(String phone);

    void process2(String msg);
}
