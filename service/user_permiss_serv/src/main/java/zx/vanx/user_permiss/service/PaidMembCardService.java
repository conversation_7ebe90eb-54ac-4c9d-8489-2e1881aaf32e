package zx.vanx.user_permiss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import zx.vanx.ma_manage.entity.VanxMemberCardInfo;
import zx.vanx.user_permiss.entity.PaidMembCard;
import zx.vanx.user_permiss.vo.UserMemberRightsCategVo;

import java.util.List;

/**
 * <p>
 * 用户已购买的会员卡 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
public interface PaidMembCardService extends IService<PaidMembCard> {

    /**
     * 查询用户会员权益分类列表
     * @param userId 用户id
     * @return 用户会员权益信息
     */
    List<UserMemberRightsCategVo> selectUserMemberRights(Long userId);

}
