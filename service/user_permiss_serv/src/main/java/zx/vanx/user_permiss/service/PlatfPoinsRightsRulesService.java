package zx.vanx.user_permiss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;
import zx.vanx.user_permiss.entity.PlatfPoinsRightsRules;
import zx.vanx.user_permiss.request.PlatfPoinsRightsAddRequest;
import zx.vanx.user_permiss.vo.UserRightsVo;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-18
 */
public interface PlatfPoinsRightsRulesService extends IService<PlatfPoinsRightsRules> {

    /**
     * 查询该用户能够添加的文件总数，通过文件类型、行为、购物积分、游戏积分
     * @param userId 用户id
     * @param fileType 文件类型
     * @return 可添加数
     */
    Long selectTotalRuleObjectValue(Long userId, String fileType);

    /**
     * 根据等级id查询-权益列表
     * @param levelInfoId 等级id
     * @return 权益列表
     */
    List<PlatfPoinsRightsRules> queryRightList(Long levelInfoId);

    /**
     * 查询该等级未添加用户行为列表
     * @param levelInfoId 等级id
     * @return 用户行为列表
     */
    List<String> queryUserBehaviorList(Long levelInfoId);

    /**
     * 查询该等级未添加权益名称列表
     * @param levelInfoId 等级id
     * @return 权益名称列表
     */
    List<String> queryUserRoleNameList(Long levelInfoId);


    /**
     * 查询权益单位
     * @return 权益单位列表
     */
    List<String> queryRuleObjectTypeList();

    /**
     * 查询权益名称列表
     * @return 权益名称列表
     */
    List<String> queryRightsNameList();

    /**
     * 添加平台积分权益
     * @param platfPoinsRightsAddRequest 平台积分规则值对象
     * @param file 所属类别图标文件
     */
    void savePlatfPointRights(PlatfPoinsRightsAddRequest platfPoinsRightsAddRequest, MultipartFile file);

    /**
     * 查询用户等级权益信息
     * @param userId 用户id
     * @return 用户等级权益信息
     */
    UserRightsVo selectLevelRights(Long userId);
}
