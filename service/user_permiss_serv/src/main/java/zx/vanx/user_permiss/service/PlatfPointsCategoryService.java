package zx.vanx.user_permiss.service;

import zx.vanx.user_permiss.entity.PlatfPointsCategory;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * Vanx系统的积分的类别表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
public interface PlatfPointsCategoryService extends IService<PlatfPointsCategory> {

    /**
     * 查询积分类别
     * @param internalPointCategName 积分类别名称
     * @return 积分类别
     */
    PlatfPointsCategory getPlatfPointsCategoryByInternalPointCategName(String internalPointCategName);

}
