package zx.vanx.user_permiss.service;

import zx.vanx.user_permiss.entity.PlatfRoles;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 角色表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-07
 */
public interface PlatfRolesService extends IService<PlatfRoles> {

    /**
     * 根据角色名查询角色信息
     * @param roleName 角色名
     * @return 角色信息
     */
    PlatfRoles selectOneByUserName(String roleName);
}
