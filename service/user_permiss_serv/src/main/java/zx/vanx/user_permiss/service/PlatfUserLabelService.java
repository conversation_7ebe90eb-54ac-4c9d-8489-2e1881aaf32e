package zx.vanx.user_permiss.service;

import zx.vanx.user_permiss.entity.PlatfUserLabel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * vanx_platf_user_label 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
public interface PlatfUserLabelService extends IService<PlatfUserLabel> {

    /**
     * 查询用户通用标签列表
     * @return 用户通用标签列表
     */
    List<PlatfUserLabel> selectPublicList();


    /**
     * 添加用户标签
     * @param platfUserLabel 公共标签列表
     */
    void addUserLabel(List<PlatfUserLabel> platfUserLabel);

    /**
     * 删除用户标签
     * @param platfUserLabel 用户标签
     */
    void deleteUserLabel(PlatfUserLabel platfUserLabel);

    /**
     * 查询用户标签列表
     * @param currentUserId 当前用户id
     * @return 用户标签列表
     */
    List<PlatfUserLabel> selectUserLabelList(Long currentUserId);

    /**
     * 所有公共标签列表
     * @return 公共标签列表
     */
    List<PlatfUserLabel> selectAllPublicList();
}
