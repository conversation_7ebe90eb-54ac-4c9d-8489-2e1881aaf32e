package zx.vanx.user_permiss.service;

import zx.vanx.user_permiss.dto.AddUserRoleDto;
import zx.vanx.user_permiss.entity.PlatfUserRoles;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 用户角色关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-07
 */
public interface PlatfUserRolesService extends IService<PlatfUserRoles> {

    /**
     * 根据用户ID查询用户角色关联表
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 用户角色关联表
     */
    PlatfUserRoles selectOneByUserIdAndRoleId(Long userId,Long roleId);

    /**
     * 添加用户角色关联表
     * @param addUserRoleDto 添加用户角色关联表参数
     * @return 是否添加成功
     */
    boolean addUserRoleIdentity(AddUserRoleDto addUserRoleDto);
}
