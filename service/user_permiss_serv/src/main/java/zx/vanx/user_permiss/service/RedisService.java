package zx.vanx.user_permiss.service;

import zx.vanx.user_permiss.entity.PlatfPoinsRightsRules;

import java.util.List;

/**
 * author Date: 2023/12/2114:36
 **/
public interface RedisService {

    /**
     * redis添加用户权益
     * 
     * @param userId 用户id
     */
    void addUserRights(Long userId);

    /**
     * redis查询用户权益
     * 
     * @param userId 用户id
     * @return
     */
    List<PlatfPoinsRightsRules> selectUserRights(Long userId);
}
