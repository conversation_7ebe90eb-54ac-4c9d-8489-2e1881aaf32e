package zx.vanx.user_permiss.service;

import zx.vanx.user_permiss.entity.UserAddress;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 用户地址表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
public interface UserAddressService extends IService<UserAddress> {

    /**
     * 修改-用户故乡信息
     * @param userAddress 用户故乡信息
     */
    void saveOrUpdateUserHometown(UserAddress userAddress);

    /**
     * 查询用户收件地址列表
     * @param userId 用户id
     * @return 用户收件地址列表
     */
    List<UserAddress> queryUserRecieveAddressList(Long userId);

    /**
     * 新增或修改用户收件地址
     * @param userAddress 用户收件地址
     */
    void saveOrUpdateUserRecieveAddress(UserAddress userAddress);

    /**
     * 删除用户收件地址
     * @param userAddress 用户收件地址
     */
    void removeUserRecieveAddress(UserAddress userAddress);

    /**
     * 修改-用户所在地信息
     * @param userAddress 用户所在地信息
     */
    void saveOrUpdateUserLocation(UserAddress userAddress);
}
