package zx.vanx.user_permiss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import zx.vanx.user_permiss.dto.UserAuthorizeDTO;
import zx.vanx.user_permiss.entity.UserAuthorize;

import java.util.List;

/**
 * <p>
 * 用户授权表(即各种登录方式，支持第三方登录) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
public interface UserAuthorizeService extends IService<UserAuthorize> {

    /**
     * 根据身份类型和标识符查询授权信息
     */
    UserAuthorize findByTypeAndIdentifier(String identityType, String identifier);

    /**
     * 保存用户授权信息
     */
    boolean saveAuthorize(UserAuthorizeDTO userAuthorizeDTO);

    /**
     * 更新用户授权信息
     */
    boolean updateAuthorize(UserAuthorizeDTO userAuthorizeDTO);

    /**
     * 根据用户ID和身份类型查询授权信息
     */
    UserAuthorizeDTO findByUserIdAndType(Long userId, String identityType);

    /**
     * 根据用户ID查询所有授权信息
     */
    List<UserAuthorizeDTO> findByUserId(Long userId);


}
