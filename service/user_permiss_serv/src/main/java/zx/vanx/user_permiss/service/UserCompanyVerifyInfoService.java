package zx.vanx.user_permiss.service;

import zx.vanx.user_permiss.entity.UserCompanyVerifyInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 用户企业认证 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
public interface UserCompanyVerifyInfoService extends IService<UserCompanyVerifyInfo> {

    /**
     * 查询是否有企业认证记录
     * @param userId 用户id
     * @return 认证记录id
     */
    Long selectOneByUserId(Long userId);

    /**
     * 查询用户企业认证基本信息list
     * @param userId 用户id
     * @return 企业认证基本信息list
     */
    List<UserCompanyVerifyInfo> selectListByUserId(Long userId);

    /**
     * 查询用户企业认证基本信息列表
     * @param userId 用户id
     * @return 企业认证基本信息列表
     */
    List<UserCompanyVerifyInfo> selectUserCompanyAuthInfoList(Long userId);
}
