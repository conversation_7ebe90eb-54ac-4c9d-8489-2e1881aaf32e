package zx.vanx.user_permiss.service;

import zx.vanx.user_permiss.entity.UserExtendInfo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
public interface UserExtendInfoService extends IService<UserExtendInfo> {

    /**
     * 修改-用户真实信息
     * @param userExtendInfo 用户真实信息
     */
    void saveOrUpdateUserExtendInfo(UserExtendInfo userExtendInfo);

}
