package zx.vanx.user_permiss.service;

import zx.vanx.user_permiss.entity.UserInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import zx.vanx.user_permiss.model.LoginUser;
import zx.vanx.user_permiss.dto.UserInfoDTO;

/**
 * <p>
 * 用户基本信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
public interface UserInfoService extends IService<UserInfo> {

    /**
     * 根据手机号查询用户信息
     * 
     * @param tel
     * @return
     */
    UserInfo selectUserInfoByTel(String tel);

    /**
     * 根据用户id查询用户信息
     * 
     * @param userId 用户id
     * @return 用户信息
     */
    UserInfo selectOneByUserId(Long userId);

    /**
     * 根据手机号查询登录用户信息
     * 
     * @param tel 手机号
     * @return 用户信息
     */
    LoginUser getUserInfo(String tel);

    /**
     * 根据用户ID查询用户信息
     */
    UserInfoDTO getUserById(Long id);

    /**
     * 保存用户信息
     */
    UserInfoDTO saveUser(UserInfoDTO userInfoDTO);

    /**
     * 更新用户信息
     */
    boolean updateUser(UserInfoDTO userInfoDTO);

    /**
     * 根据手机号查询用户信息
     */
    UserInfoDTO findByPhone(String phone);

    /**
     * 根据手机号查询登录用户信息
     * @param userId userId
     * @return 登录用户信息
     */
    LoginUser getLoginUserByUserId(Long userId);
}
