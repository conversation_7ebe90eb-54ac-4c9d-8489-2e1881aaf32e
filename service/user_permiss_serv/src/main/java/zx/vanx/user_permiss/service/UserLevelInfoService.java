package zx.vanx.user_permiss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import zx.vanx.user_permiss.entity.PlatfUserLevelInfo;
import zx.vanx.user_permiss.vo.UserLevelSelectVo;
import zx.vanx.user_permiss.vo.VipCardLevelRightsVo;

import java.util.List;

/**
 * <p>
 * vanx_platf_user_level_info 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
public interface UserLevelInfoService extends IService<PlatfUserLevelInfo> {

    /**
     * 查询等级及其等级下的权益列表
     * @param userLevelType 等级类型
     * @return 等级及其等级下的权益列表
     */
    List<PlatfUserLevelInfo> queryLevelAndRightsList(String userLevelType);

    /**
     * 删除等级
     * @param levelInfoId 等级id
     */
    void removeLevelInfo(Long levelInfoId);

    /**
     * 查询用户等级下拉列表
     * @param userLevelType 用户等级类型
     * @return 用户等级下拉列表
     */
    List<UserLevelSelectVo> queryUserLevelSelectList(String userLevelType);

    /**
     * 会员卡查询等级权益基本信息
     * @param userBehaviorType 用户行为类型
     * @param levelInfoId 用户等级id
     * @return 等级权益基本信息
     */
    VipCardLevelRightsVo queryVipCardLevelRights(String userBehaviorType,Long levelInfoId);

    /**
     * 根据等级id查询等级信息
     * @param levelInfoId 等级id
     * @return 等级信息
     */
    PlatfUserLevelInfo queryUserLevelInfo(Long levelInfoId);
}
