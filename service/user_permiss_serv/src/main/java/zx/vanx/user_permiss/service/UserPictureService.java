package zx.vanx.user_permiss.service;

import org.springframework.web.multipart.MultipartFile;
import zx.vanx.user_permiss.entity.UserPicture;
import com.baomidou.mybatisplus.extension.service.IService;
import zx.vanx.user_permiss.vo.query.UserSelectedPictureVo;

import java.util.List;

/**
 * <p>
 * user_picture 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
public interface UserPictureService extends IService<UserPicture> {

    /**
     * 保存用户精选照片
     * @param file 精选照片文件
     */
    void saveUserSelectedPicture(MultipartFile[] file);


    /**
     * 删除用户精选照片
     * @param imagesList 精选照片列表
     */
    void deleteUserSelectedPicture(List<UserPicture> imagesList);

    /**
     * 查询当前用户的精选照片列表
     * @param currentUserId 当前用户id
     */
    UserSelectedPictureVo selectUserSelectedPicture(Long currentUserId);



    /**
     * 查询当前用户的头像照片
     * @param currentUserId 当前用户id
     * @return 头像照片
     */
    UserPicture selectUserAvatarPicture(Long currentUserId);

    /**
     * 查询当前用户的封面照片
     * @param currentUserId 当前用户id
     * @return 封面照片
     */
    UserPicture selectUserCover(Long currentUserId);

    /**
     * 个人头像-修改
     * @param userAvatar 用户头像
     * @param file 头像文件
     */
    UserPicture updateUserPicture(UserPicture userAvatar, MultipartFile file);
}
