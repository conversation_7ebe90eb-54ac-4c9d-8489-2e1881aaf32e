package zx.vanx.user_permiss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import zx.vanx.user_permiss.entity.UserPointsAddLog;
import zx.vanx.user_permiss.request.NoSystemPointsAddRequest;
import zx.vanx.user_permiss.request.SystemPointsAddRequest;
import zx.vanx.user_permiss.vo.DynamicPointsVo;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
public interface UserPointsAddLogService extends IService<UserPointsAddLog> {

    /**
     * 用户加积分
     * @param systemPointsAddRequest 系统加积分请求对象
     * @param userId 用户id
     */
    void systemPointsGain(SystemPointsAddRequest systemPointsAddRequest,Long userId);

    /**
     * 非系统配置行为积分-增益
     * @param noSystemPointsAddRequest 非系统配置行为积分对象
     * @param userId 用户id
     */
    void noSystemPointsGain(NoSystemPointsAddRequest noSystemPointsAddRequest,Long userId);

    /**
     * 查询用户收入的动态积分记录list
     * @param userId 用户id
     * @param recentDateList 最近日期列表
     * @return 用户收入的动态积分记录list
     */
    List<DynamicPointsVo> selectUserRecentPointsList(Long userId, List<String> recentDateList);
}
