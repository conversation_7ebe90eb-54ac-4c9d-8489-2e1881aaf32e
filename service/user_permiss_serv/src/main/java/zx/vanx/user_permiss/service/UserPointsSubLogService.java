package zx.vanx.user_permiss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import zx.vanx.user_permiss.entity.UserPointsSubLog;
import zx.vanx.user_permiss.request.NoSystemPointsReduceRequest;
import zx.vanx.user_permiss.vo.DynamicPointsVo;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-19
 */
public interface UserPointsSubLogService extends IService<UserPointsSubLog> {

    /**
     * 查询用户支出的动态积分记录list
     * @param userId 用户id
     * @param recentDateList 近期日期列表
     * @return 动态积分记录list
     */
    List<DynamicPointsVo> selectUserRecentPointsList(Long userId, List<String> recentDateList);

    /**
     * 非系统配置行为积分-减少
     * @param noSystemPointsReduceRequest 非系统配置行为积分-减少请求
     * @param userId 用户id
     */
    void noSystemPointsReduce(NoSystemPointsReduceRequest noSystemPointsReduceRequest, Long userId);
}
