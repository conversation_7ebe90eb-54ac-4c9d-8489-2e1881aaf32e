package zx.vanx.user_permiss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import zx.vanx.user_permiss.entity.UserPrivSetting;
import zx.vanx.user_permiss.vo.AppPassword;
import zx.vanx.user_permiss.vo.loginAppInfo;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface UserPrivSettingService extends IService<UserPrivSetting> {

    /**
     * 用户是否开启隐藏功能
     * @param currentUserId 当前用户ID
     * @return true:开启隐藏功能，false:未开启隐藏功能
     */
    Boolean userIsOpenHideFunction(Long currentUserId);

    /**
     * 当前用户是否设置启动时加密，若设置，密码类型是啥
     * @param currentUserId 当前用户ID
     * @param encryptMethod 加密方式 : 启动时加密、进入应用加密、查看作品加密
     * @return AppPassword
     */
    AppPassword userIsSettingEncryptMethod(Long currentUserId, String encryptMethod);

    /**
     * App用户登录
     * @param loginAppInfo 用户登录信息
     * @return true:登录成功，false:登录失败
     */
    Boolean loginApp(loginAppInfo loginAppInfo);

}
