package zx.vanx.user_permiss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import zx.vanx.user_permiss.entity.UserInfo;
import zx.vanx.user_permiss.entity.UserRegister;

/**
 * <p>
 * 用户注册，登录验证表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
public interface UserRegisterService extends IService<UserRegister> {

    /**
     * 通过手机号查询注册表信息
     * @param tel 手机号
     * @return 注册表信息
     */
    UserRegister selectOneByTel(String tel);

    /**
     * 通过手机号和角色名注册用户
     * @param tel 手机号
     * @param roleName 角色名
     */
    UserInfo registerUserByTelAndRoleName(String tel, String roleName);
}
