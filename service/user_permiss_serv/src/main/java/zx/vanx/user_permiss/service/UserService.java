package zx.vanx.user_permiss.service;

import cn.hutool.json.JSONObject;
import org.springframework.web.multipart.MultipartFile;
import zx.vanx.common_util.core.page.PageObj;
import zx.vanx.common_util.utils.ResultData;
import zx.vanx.user_permiss.entity.UserCompanyVerifyInfo;
import zx.vanx.user_permiss.entity.UserInfo;
import zx.vanx.user_permiss.entity.UserVerifyInfo;
import zx.vanx.user_permiss.excel.UserInfoExcel;
import zx.vanx.user_permiss.request.CompanyAuthUserInfoRequest;
import zx.vanx.user_permiss.request.ElecResourceUserRequest;
import zx.vanx.user_permiss.request.PersonalAuthUserInfoRequest;
import zx.vanx.user_permiss.request.UserListRequest;
import zx.vanx.user_permiss.vo.*;

import java.util.List;

/**
 * Date: 2023/7/614:59
 *
 * <AUTHOR>
 * */
public interface UserService {

    /**
     * 用户直接手机号授权登录
     * @param tel 手机号
     * @return token
     */
    ResultData<JSONObject> userLoginOnlyTel(String tel,String roleName);

    /**
     * 手机号验证码登录
     * @param tel 手机号
     * @param code 验证码
     * @return token
     */
    ResultData<JSONObject> loginCode(String tel, String code, String roleName);

    void register(String tel, String registPsw, String code,String roleName);

    /**
     * 手机号-密码登录
     * @param tel 手机号
     * @param registPsw 密码
     * @return
     */
    ResultData<JSONObject> userLoginTel(String tel, String registPsw,String roleName);

    /**
     * 获取-用户详情
     * @param userId 用户id
     * @return 用户详情
     */
    UserDetail getUserDetail(Long userId);

    /**
     * 修改-用户基本信息
     * @param userInfo 用户基本信息
     */
    void updateUserInfo(UserInfo userInfo);

    /**
     * 内部pc端修改-用户基本信息
     * @param updateUserBaseInfo 修改时的用户基本信息
     * @return 修改后的用户详情
     */
    void updateUserBaseInfo(UpdateUserBaseInfo updateUserBaseInfo);

    /**
     * app根据vanx号查询用户基本信息
     * @param vanxId vanx号
     * @return 用户基本信息
     */
    UserInfoVo queryUserInfoByVanxId(Long userId,String vanxId);

    /**
     * 获得用户当前身份信息
     * @param userId 用户id
     * @return 用户身份信息
     */
    UserIdentityConfirmVo getUserIdentityConfirm(Long userId);

    /**
     * 更新广告弹出记录
     * @param userId 用户id
     */
    void reAdPopUpRecord(Long userId);

    /**
     * 根据用户id查询用户身份信息
     * @param userId 用户id
     * @return 用户身份信息
     */
    UserIdentityConfirmVo queryUserIdentityInfo(Long userId);


    /**
     * 用户个人认证
     * @param userId 用户id
     * @param idPicFrontFile 身份证正面照片
     * @param idPicBackFile 身份证反面照片
     * @param userVerifyInfo 用户认证信息
     */
    void userPersonalAuth(Long userId, MultipartFile idPicFrontFile, MultipartFile idPicBackFile, UserVerifyInfo userVerifyInfo);

    /**
     * 用户企业认证
     * @param userId 用户id
     * @param idPicFrontFile 身份证正面照片
     * @param idPicBackFile 身份证反面照片
     * @param businessPicFile 营业执照照片
     * @param userCompanyVerifyInfo 企业认证信息
     */
    void userCompanyAuth(Long userId, MultipartFile idPicFrontFile, MultipartFile idPicBackFile, MultipartFile businessPicFile, UserCompanyVerifyInfo userCompanyVerifyInfo);

    /**
     * 查询积分中心用户基本信息
     * @param userId 用户id
     * @return 积分中心用户基本信息
     */
    IntegralCenterUserBaseInfo queryIntegralCenterUserBaseInfo(Long userId);

    /**
     * 用户注销
     * @param userId 用户id
     */
    void userLogout(Long userId);

    /**
     * 验证用户角色是否为超级管理角色
     * @param userId 用户id
     * @return 是否为超级管理角色
     */
    boolean isSuperAdmin(Long userId);

    /**
     * 获取-用户列表
     * @param userListRequest 用户列表请求参数
     * @return 用户列表
     */
    PageObj<ManagerUserInfoVo> getManagerUserInfoVoList(UserListRequest userListRequest);

    /**
     * 导入用户信息
     * @param listByFile 用户信息列表
     */
    void importUserInfo(List<UserInfoExcel> listByFile);


    /**
     * 查询用户附近的人
     * @param userId 用户id
     * @return 用户附近的人
     */
    List<NearPerson> selectNearbyUser(Long userId);

    /**
     * 用户身份切换
     * @param userId 用户id
     * @param roleName 角色名称
     */
    void userIdentitySwitch(Long userId, String roleName);

    /**
     * 用户切换身份列表查询
     * @param userId 用户id
     * @param roleCategName 角色分类名称
     * @return 用户切换身份列表
     */
    List<UserIdentitySwitch> userIdentitySwitchList(Long userId, String roleCategName);

    /**
     * 个人认证用户列表查询
     * @param personalAuthUserInfoRequest 认证来源
     * @return 个人认证用户列表
     */
    PageObj<PersonalAuthUserInfo> personalAuthUserList(PersonalAuthUserInfoRequest personalAuthUserInfoRequest);

    /**
     * 企业认证用户列表查询
     * @param companyAuthUserInfoRequest 认证来源
     * @return 企业认证用户列表
     */
    PageObj<CompanyAuthUserInfo> companyAuthUserList(CompanyAuthUserInfoRequest companyAuthUserInfoRequest);

    /**
     * 个人认证状态修改
     * @param userId 修改者用户id
     * @param userVerifyId 个人认证记录id
     * @param artificialPersonStatus 认证状态
     */
    void personalAuthStatusUpdate(Long userId,
                                  Long userVerifyId,
                                  String artificialPersonStatus,
                                  String addSource,
                                  String rejectReason);

    /**
     * 企业认证状态修改
     * @param userId 修改者用户id
     * @param userComVerifyId 企业认证记录id
     * @param artificialPersonStatus 认证状态
     */
    void companyAuthStatusUpdate(Long userId,
                                 Long userComVerifyId,
                                 String artificialPersonStatus,
                                 String addSource,
                                 String rejectReason);

    /**
     * 查询用户是否个人认证
     * @param userId 用户id
     * @return 是否个人认证
     */
    PersonalAuthInfoVo queryUserIsPersonalAuth(Long userId);

    /**
     * 查询用户是否企业认证
     * @param userId 用户id
     * @return 是否企业认证
     */
    CompanyAuthInfoVo queryUserIsCompanyAuth(Long userId);

    /**
     * 查询用户个人认证基本信息
     * @param userId 用户id
     * @return 个人认证基本信息
     */
    List<UserVerifyInfo> queryUserPersonalAuthInfoList(Long userId);

    /**
     * 查询用户企业认证基本信息list
     * @param userId 用户id
     * @return 企业认证基本信息list
     */
    List<UserCompanyVerifyInfo> queryUserCompanyAuthInfoList(Long userId);

    /**
     * 删除用户个人认证信息
     * @param userVerifyInfo 个人认证信息
     */
    void removeUserPersonalAuthInfo(UserVerifyInfo userVerifyInfo);

    /**
     * 删除用户企业认证信息
     * @param userCompanyVerifyInfo 企业认证信息
     */
    void removeUserCompanyAuthInfo(UserCompanyVerifyInfo userCompanyVerifyInfo);

    /**
     * 电子资源用户列表
     * @param userId 用户id
     * @param elecResourceUserRequest 电子资源用户请求
     * @return 电子资源用户列表
     */
    PageObj<ElecResourceUserVo> elecResourceUserList(Long userId, ElecResourceUserRequest elecResourceUserRequest);

    /**
     * 查询用户是否存在求职者个人招募身份
     * @param userId 用户id
     * @return 是否存在求职者个人招募身份
     */
    Boolean queryUserIsJobSeekIdentity(Long userId);

    /**
     * 查询用户个人认证信息列表
     * @param userId 用户id
     * @return 个人认证信息列表
     */
    List<UserVerifyInfo> selectUserPersonalAuthInfoList(Long userId);

    /**
     * 添加求职者个人招募身份
     * @param userId 用户id
     */
    void addUserJobSeekIdentity(Long userId);

    /**
     * 添加企业认证身份
     * @param userId 用户id
     */
    void addUserCompanyIdentity(Long userId);

    /**
     * 查询用户是否存在求职者企业招募身份
     * @param userId 用户id
     * @return 是否存在求职者企业招募身份
     */
    Boolean queryUserIsCompanyIdentity(Long userId);
}
