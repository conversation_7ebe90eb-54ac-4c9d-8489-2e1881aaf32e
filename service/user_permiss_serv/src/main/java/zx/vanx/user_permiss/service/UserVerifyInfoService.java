package zx.vanx.user_permiss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import zx.vanx.user_permiss.entity.UserVerifyInfo;

import java.util.List;

/**
 * <p>
 * 用户个人认证 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
public interface UserVerifyInfoService extends IService<UserVerifyInfo> {

    /**
     * 查询是否有个人认证记录
     * @param userId 用户id
     * @return 记录数
     */
    Long selectCountByUserId(Long userId);


    /**
     * 查询用户个人认证基本信息列表
     * @param userId 用户id
     * @return 个人认证基本信息列表
     */
    List<UserVerifyInfo> selectListByUserId(Long userId);


}
