package zx.vanx.user_permiss.service.impl;

import zx.vanx.user_permiss.entity.AdDispUserLog;
import zx.vanx.user_permiss.mapper.AdDispUserLogMapper;
import zx.vanx.user_permiss.service.AdDispUserLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 系统弹出广告的用户端记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
@Service
public class AdDispUserLogServiceImpl extends ServiceImpl<AdDispUserLogMapper, AdDispUserLog> implements AdDispUserLogService {

}
