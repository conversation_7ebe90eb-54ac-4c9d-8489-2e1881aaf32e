package zx.vanx.user_permiss.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import zx.vanx.user_permiss.entity.AppResourceInfo;
import zx.vanx.user_permiss.mapper.AppResourceInfoMapper;
import zx.vanx.user_permiss.service.AppResourceInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 平台的app端系统资源表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-05
 */
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class AppResourceInfoServiceImpl extends ServiceImpl<AppResourceInfoMapper, AppResourceInfo> implements AppResourceInfoService {

}
