package zx.vanx.user_permiss.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.exception.ZxException;
import zx.vanx.user_permiss.entity.AppSettingRules;
import zx.vanx.user_permiss.entity.AppSettingRulesValue;
import zx.vanx.user_permiss.entity.UserPrivSetting;
import zx.vanx.user_permiss.mapper.AppSettingRulesMapper;
import zx.vanx.user_permiss.mapper.AppSettingRulesValueMapper;
import zx.vanx.user_permiss.mapper.UserPrivSettingMapper;
import zx.vanx.user_permiss.service.AppSettingRulesService;

import java.util.List;

/**
 * <p>
 * 平台系统设置规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-10
 */
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class AppSettingRulesServiceImpl extends ServiceImpl<AppSettingRulesMapper, AppSettingRules> implements AppSettingRulesService {

    private final AppSettingRulesMapper appSettingRulesMapper;

    private final AppSettingRulesValueMapper appSettingRulesValueMapper;

    private final UserPrivSettingMapper userPrivSettingMapper;

    /**
     * 查询平台系统设置规则表列表
     * @param appRuleId 父级id
     * @return 平台规则列表
     */
    @Override
    public AppSettingRules selectAppSettingRulesOne(Long appRuleId) {

        Long userId = LoginHelper.getUserId();

        AppSettingRules appSettingRulesById = appSettingRulesMapper.selectById(appRuleId);

        if (ObjectUtils.isEmpty(appSettingRulesById)) {
            throw new ZxException(201,"配置规则不存在");
        }

        List<AppSettingRules> appSettingRulesList = appSettingRulesMapper.selectAllByParentAppRuleId(appRuleId);

        if (!CollectionUtils.isEmpty(appSettingRulesList)) {
            appSettingRulesById.setChildren(appSettingRulesList);
        }

        if (appSettingRulesById.getHasRulesValueForUser()) {

            List<AppSettingRulesValue> appSettingRulesValues = appSettingRulesValueMapper.selectAllByAppRuleId(appSettingRulesById.getAppRuleId());
            UserPrivSetting userPrivSetting = userPrivSettingMapper.selectOneByUserIdAndRuleId(userId, appSettingRulesById.getAppRuleId());

            if (!ObjectUtils.isEmpty(userPrivSetting)) {
                appSettingRulesValues.forEach(appSettingRulesValue -> {
                    appSettingRulesValue.setIsDefaultValue(false);
                    if (userPrivSetting.getRuleValueId().equals(appSettingRulesValue.getRuleValueId())) {
                        appSettingRulesValue.setIsDefaultValue(true);
                    }
                });
            }

            appSettingRulesById.setAppSettingRulesValueList(appSettingRulesValues);
        }

        return appSettingRulesById;
    }

    /**
     * 查询-顶层设置列表
     * @param parentAppRuleId 父级id
     * @return 平台规则列表
     */
    @Override
    public List<AppSettingRules> selectAppSettingRulesList(Long parentAppRuleId) {

        return appSettingRulesMapper.selectAllByParentAppRuleId(parentAppRuleId);
    }
}
