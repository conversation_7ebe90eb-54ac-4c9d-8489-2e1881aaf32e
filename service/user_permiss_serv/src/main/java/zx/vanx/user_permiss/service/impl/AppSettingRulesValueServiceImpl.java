package zx.vanx.user_permiss.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import zx.vanx.user_permiss.entity.AppSettingRulesValue;
import zx.vanx.user_permiss.mapper.AppSettingRulesValueMapper;
import zx.vanx.user_permiss.service.AppSettingRulesValueService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 平台规则取值表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-10
 */
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class AppSettingRulesValueServiceImpl extends ServiceImpl<AppSettingRulesValueMapper, AppSettingRulesValue> implements AppSettingRulesValueService {

}
