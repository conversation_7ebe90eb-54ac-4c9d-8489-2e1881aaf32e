package zx.vanx.user_permiss.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zx.vanx.user_permiss.entity.GrowthValueWeight;
import zx.vanx.user_permiss.entity.UserInfoFast;
import zx.vanx.user_permiss.mapper.GrowthValueWeightMapper;
import zx.vanx.user_permiss.mapper.PlatfUserLevelInfoMapper;
import zx.vanx.user_permiss.service.GrowthValueWeightService;

/**
 * <p>
 * vanx_growth_value_weight 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class GrowthValueWeightServiceImpl extends ServiceImpl<GrowthValueWeightMapper, GrowthValueWeight> implements GrowthValueWeightService {

    private final PlatfUserLevelInfoMapper userLevelInfoMapper;

    private final GrowthValueWeightMapper growthValueWeightMapper;

    /**
     * 增加用户成长值
     * @param userInfoFast 用户信息
     */
    @Override
    public UserInfoFast addUserGrowthValueByGrowthValueWeight(UserInfoFast userInfoFast) {

        Double growthValue = userInfoFast.getGrowthValue(); // 获取用户成长值
        Long userLevel = userLevelInfoMapper.selectLevelId(growthValue, "成长值等级"); // 查询用户等级id
        GrowthValueWeight growthValueWeight = growthValueWeightMapper.selectOneByLevelInfoId(userLevel); // 查询用户等级对应的成长值权重

        Double result
                = userInfoFast.getUserBehaviorPoints() * growthValueWeight.getBehaviorPointsWeight()
                + userInfoFast.getUserShoppingPoints() * growthValueWeight.getShoppingPointsWeight()
                + userInfoFast.getUserGamePoints() * growthValueWeight.getGamePointsWeight();

        userInfoFast.setGrowthValue(result);

        return userInfoFast;

    }
}
