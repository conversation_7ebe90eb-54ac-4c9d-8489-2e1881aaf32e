package zx.vanx.user_permiss.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import zx.vanx.user_permiss.entity.PaidMembRightsRules;
import zx.vanx.user_permiss.mapper.PaidMembRightsRulesMapper;
import zx.vanx.user_permiss.service.PaidMembRightsRulesService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * vanx_platf_user_pay_rules 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class PaidMembRightsRulesServiceImpl extends ServiceImpl<PaidMembRightsRulesMapper, PaidMembRightsRules> implements PaidMembRightsRulesService {

}
