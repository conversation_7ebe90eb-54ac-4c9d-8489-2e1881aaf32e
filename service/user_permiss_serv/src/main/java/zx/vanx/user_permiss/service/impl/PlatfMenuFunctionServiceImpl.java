package zx.vanx.user_permiss.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import zx.vanx.user_permiss.entity.PlatfMenuFunction;
import zx.vanx.user_permiss.mapper.PlatfMenuFunctionMapper;
import zx.vanx.user_permiss.service.PlatfMenuFunctionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 菜单功能表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class PlatfMenuFunctionServiceImpl extends ServiceImpl<PlatfMenuFunctionMapper, PlatfMenuFunction> implements PlatfMenuFunctionService {

}
