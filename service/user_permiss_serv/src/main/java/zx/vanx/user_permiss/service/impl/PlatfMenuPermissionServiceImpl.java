package zx.vanx.user_permiss.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import zx.vanx.user_permiss.entity.PlatfMenuPermission;
import zx.vanx.user_permiss.mapper.PlatfMenuPermissionMapper;
import zx.vanx.user_permiss.service.PlatfMenuPermissionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 菜单权限功能表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class PlatfMenuPermissionServiceImpl extends ServiceImpl<PlatfMenuPermissionMapper, PlatfMenuPermission> implements PlatfMenuPermissionService {

}
