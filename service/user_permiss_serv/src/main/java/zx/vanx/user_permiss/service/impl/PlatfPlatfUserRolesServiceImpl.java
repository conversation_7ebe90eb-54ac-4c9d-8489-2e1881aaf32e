package zx.vanx.user_permiss.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zx.vanx.common_util.exception.ZxException;
import zx.vanx.common_util.utils.ObjectUtil;
import zx.vanx.user_permiss.dto.AddUserRoleDto;
import zx.vanx.user_permiss.entity.PlatfRoles;
import zx.vanx.user_permiss.entity.PlatfUserRoles;
import zx.vanx.user_permiss.mapper.PlatfRolesMapper;
import zx.vanx.user_permiss.mapper.PlatfUserRolesMapper;
import zx.vanx.user_permiss.service.PlatfUserRolesService;

/**
 * <p>
 * 用户角色关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-07
 */
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class PlatfPlatfUserRolesServiceImpl extends ServiceImpl<PlatfUserRolesMapper, PlatfUserRoles> implements PlatfUserRolesService {

    private final PlatfRolesMapper platfRolesMapper;

    /**
     * 根据用户ID查询用户角色关联表
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 用户角色关联表
     */
    @Override
    public PlatfUserRoles selectOneByUserIdAndRoleId(Long userId, Long roleId) {
        QueryWrapper<PlatfUserRoles> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        wrapper.eq("role_id", roleId);
        return this.getOne(wrapper);
    }

    /**
     * 添加用户角色身份
     * @param addUserRoleDto 添加用户角色身份参数
     * @return 是否添加成功
     */
    @Override
    public boolean addUserRoleIdentity(AddUserRoleDto addUserRoleDto) {

        // 根据角色名查询角色信息
        PlatfRoles platfRoles = platfRolesMapper.selectOneByRoleName(addUserRoleDto.getRoleName());

        PlatfUserRoles platfUserRoles = new PlatfUserRoles();
        platfUserRoles.setUserId(addUserRoleDto.getUserId());
        platfUserRoles.setRoleId(platfRoles.getRoleId());

        // 查询用户是否已经存在该角色
        PlatfUserRoles selectPlatfUserRoles = this.selectOneByUserIdAndRoleId(addUserRoleDto.getUserId(),platfRoles.getRoleId());
        if (!ObjectUtil.isEmpty(selectPlatfUserRoles)) {
            throw new ZxException(204,"用户已经存在该角色");
        }

        this.save(platfUserRoles);
        return true;
    }
}
