package zx.vanx.user_permiss.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;
import zx.vanx.common.satoken.utils.LoginHelper;
import zx.vanx.common_util.core.redis.RedisCache;
import zx.vanx.common_util.utils.BeanUtil;
import zx.vanx.ma_manage.client.ManageClient;
import zx.vanx.ma_manage.entity.VanxMemberCardInfo;
import zx.vanx.service.MinioService;
import zx.vanx.user_permiss.entity.*;
import zx.vanx.user_permiss.mapper.PaidMembCardMapper;
import zx.vanx.user_permiss.mapper.PlatfPoinsRightsRulesMapper;
import zx.vanx.user_permiss.mapper.PlatfUserLevelInfoMapper;
import zx.vanx.user_permiss.mapper.UserInfoFastMapper;
import zx.vanx.user_permiss.redis.key.PermissKey;
import zx.vanx.user_permiss.redis.service.UserInfoFastRedisService;
import zx.vanx.user_permiss.request.PlatfPoinsRightsAddRequest;
import zx.vanx.user_permiss.service.PaidMembCardService;
import zx.vanx.user_permiss.service.PlatfPoinsRightsCategService;
import zx.vanx.user_permiss.service.PlatfPoinsRightsRulesService;
import zx.vanx.user_permiss.vo.*;
import zx.vanx.vo.ImageData;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-18
 */
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class PlatfPoinsRightsRulesServiceImpl extends ServiceImpl<PlatfPoinsRightsRulesMapper, PlatfPoinsRightsRules> implements PlatfPoinsRightsRulesService {


    private final PlatfPoinsRightsRulesMapper platformPoinsRulesMapper;

    private final UserInfoFastMapper userInfoFastMapper;

    private final PlatfUserLevelInfoMapper userLevelInfoMapper;

    private final MinioService minioService;

    private final PlatfUserLevelInfoMapper platfUserLevelInfoMapper;

    private final UserInfoFastRedisService userInfoFastRedisService;

    private final RedisCache redisCache;

    private final PaidMembCardMapper paidMembCardMapper;

    private final ManageClient manageClient;



    private final PaidMembCardService paidMembCardService;
    private final PlatfPoinsRightsCategService platfPoinsRightsCategService;
    private final PlatfPoinsRightsRulesMapper platfPoinsRightsRulesMapper;

    /**
     * 查询该用户能够添加的文件总数，通过文件类型、成长值、购物积分、游戏积分
     * @param userId 用户id
     * @param userBehaviorType 文件类型
     * @return 可添加数
     */
    @Override
    public Long selectTotalRuleObjectValue(Long userId, String userBehaviorType) {

        UserSystemRightsVo userSystemRightsVo = platfPoinsRightsRulesMapper.selectUserSystemRight(userId, userBehaviorType);


        // 查询用户fastinfo表
        UserInfoFast userInfoFast = userInfoFastMapper.selectOneByUserId(userId);

//        Long growthValueLevelId = userLevelInfoMapper.selectLevelId(userInfoFast.getGrowthValue(),"行为等级");
//        Long userShoppingPointsLevelId = userLevelInfoMapper.selectLevelId(userInfoFast.getUserShoppingPoints(),"购物等级");
//        Long userGamePointsLevelId = userLevelInfoMapper.selectLevelId(userInfoFast.getUserGamePoints(),"游戏等级");
//
//        Long total = platformPoinsRulesMapper.selectTotalRuleObjectValue(userBehaviorType, growthValueLevelId, userShoppingPointsLevelId, userGamePointsLevelId);
//
//        if( ObjectUtils.isEmpty(total) ||total == 0) {
//            throw new ZxException(20001,"userBehaviorType:用户事件行为输入错误或不存在");
//        }

        // 返回可添加数
        return userSystemRightsVo.getTotalRuleObjectValue();

    }

    /**
     * 根据等级id查询-权益列表
     * @param levelInfoId 等级id
     * @return 权益列表
     */
    @Override
    public List<PlatfPoinsRightsRules> queryRightList(Long levelInfoId) {

        return platformPoinsRulesMapper.selectList(new QueryWrapper<PlatfPoinsRightsRules>()
                .eq("level_info_id", levelInfoId)
                .orderByAsc("rule_object_type")
                .orderByAsc("rule_object_value")
                .orderByDesc("created_time"));
    }

    /**
     * 查询该等级未添加用户行为列表
     * @param levelInfoId 等级id
     * @return 用户行为列表
     */
    @Override
    public List<String> queryUserBehaviorList(Long levelInfoId) {

        return platformPoinsRulesMapper.selectUserBehaviorList(levelInfoId);
    }

    /**
     * 查询该等级未添加权益名称列表
     * @param levelInfoId 等级id
     * @return 权益名称列表
     */
    @Override
    public List<String> queryUserRoleNameList(Long levelInfoId) {
        return platformPoinsRulesMapper.selecUserRoleNameList(levelInfoId);
    }

    /**
     * 查询权益单位
     * @return 权益单位列表
     */
    @Override
    public List<String> queryRuleObjectTypeList() {

        return platformPoinsRulesMapper.selectRuleObjectTypeList();
    }

    /**
     * 查询权益名称列表
     * @return 权益名称列表
     */
    @Override
    public List<String> queryRightsNameList() {
        return baseMapper.selectRightsNameList();
    }

    /**
     * 添加平台积分权益
     * @param platfPoinsRightsAddRequest 平台积分规则值对象
     * @param file 所属类别图标文件
     */
    @Override
    public void savePlatfPointRights(PlatfPoinsRightsAddRequest platfPoinsRightsAddRequest, MultipartFile file) {

        // 将dto转换为实体类
        PlatfPoinsRightsRules platfPoinsRightsRules = BeanUtil.copyProperties(platfPoinsRightsAddRequest, PlatfPoinsRightsRules::new);
        // 上传minio获取url
        ImageData imageData = minioService.uploadFile(file, LoginHelper.getUserId());
        // 设置所属类别图标url
        platfPoinsRightsRules.setRuleCategoryUrl(imageData.getUrl());
        // 设置所属类别图标minio名称
        platfPoinsRightsRules.setRuleCategoryMinioName(imageData.getFileName());

        // 保存平台积分权益
        platformPoinsRulesMapper.insert(platfPoinsRightsRules);

    }

    /**
     * 查询用户等级权益信息
     * @param userId 用户id
     * @return 用户等级权益信息
     */
    @Override
    public UserRightsVo selectLevelRights(Long userId) {

        UserRightsVo userRightsVo = new UserRightsVo();

        UserInfoFast userInfoFast = userInfoFastRedisService.getUserInfoFastByUserId(userId);

        UserDetail userDetail = redisCache.getCacheObject(PermissKey.USER_DETAIL + userId);
        UserInfo userInfo = userDetail.getUserInfo();
        UserPicture userAvatar = userDetail.getUserAvatar();

        // 设置用户昵称
        userRightsVo.setUserNickname(userInfo.getUserNickname());

        // 设置用户头像
        userRightsVo.setUserAvatar(userAvatar.getPictureUrl());

        //设置用户vanxId
        userRightsVo.setUserInternalCode(userInfo.getUserInternalCode());

        // 设置用户是否是会员用户
        userRightsVo.setIsPaidMembUser(userInfoFast.getIsPaidMembUser());

        // 设置用户成长值
        userRightsVo.setGrowthValue(userInfoFast.getGrowthValue());

        // 查询用户当前等级
        PlatfUserLevelInfo platfUserLevelInfo = platfUserLevelInfoMapper.selectUserLevelInfo(userId);
        String userLevelName = platfUserLevelInfo.getUserLevelName();// 当前等级名称
        String userLevelIcon = platfUserLevelInfo.getUserLevelIcon();// 当前等级图标
        userRightsVo.setUserLevelName(userLevelName);
        userRightsVo.setUserLevelIcon(userLevelIcon);
        userRightsVo.setUserCurrentLevelExp(userInfoFast.getGrowthValue() - platfUserLevelInfo.getRequiredMinPoins());
        userRightsVo.setUserCurrentLevelInitExp(Double.valueOf(platfUserLevelInfo.getRequiredMinPoins()));

        // 查询用户下一级
        PlatfUserLevelInfo maxPlatfUserLevelInfo = platfUserLevelInfoMapper.selectLevelInfoBySort(platfUserLevelInfo.getSortNumber() + 1);
        if (!ObjectUtils.isEmpty(maxPlatfUserLevelInfo)) {
            userRightsVo.setIsHasNextLevel(true);
            userRightsVo.setUserNextLevelInitExp(Double.valueOf(maxPlatfUserLevelInfo.getRequiredMinPoins()));
            userRightsVo.setUserNextLevelName(maxPlatfUserLevelInfo.getUserLevelName());
            userRightsVo.setUserNextLevelIcon(maxPlatfUserLevelInfo.getUserLevelIcon());
        } else {
            userRightsVo.setIsHasNextLevel(false);
        }

        // 查询用户上一级
        if (platfUserLevelInfo.getSortNumber().equals(1)) {
            userRightsVo.setIsHasPreLevel(false);
        } else {
            PlatfUserLevelInfo minPlatfUserLevelInfo = platfUserLevelInfoMapper.selectLevelInfoBySort(platfUserLevelInfo.getSortNumber() - 1);
            userRightsVo.setUserPreLevelInitExp(Double.valueOf(minPlatfUserLevelInfo.getRequiredMinPoins()));
            userRightsVo.setUserPreLevelName(minPlatfUserLevelInfo.getUserLevelName());
            userRightsVo.setUserPreLevelIcon(minPlatfUserLevelInfo.getUserLevelIcon());
            userRightsVo.setIsHasPreLevel(true);
        }

        // 查询用户系统权益分类列表
        List<UserSystemRightsCategVo> userSystemRightsCategVoList = this.selectUserSystemRights(userId);
        userRightsVo.setUserSystemRightsCategVoList(userSystemRightsCategVoList);

        // 查询用户会员权益分类列表
        List<UserMemberRightsCategVo> userMemberRightsCategVoList = paidMembCardService.selectUserMemberRights(userId);
        userRightsVo.setUserMemberRightsCategVoList(userMemberRightsCategVoList);

        // 查询用户当前购买的有效会员卡
        PaidMembCard paidMembCard = paidMembCardMapper.selectUserEffectiveMembCard(userId);

        // 设置会员卡到期时间
        if (!ObjectUtils.isEmpty(paidMembCard)) {
            userRightsVo.setUserRuleEndTime(paidMembCard.getUserRuleEndTime());

            VanxMemberCardInfo data = manageClient.selectIconByCardId(paidMembCard.getMemberCardId());

            if (!ObjectUtils.isEmpty(data)) {
                userRightsVo.setCardTitle(data.getCardTitle()); // 设置会员卡名称
                userRightsVo.setCardType(data.getCardType()); // 设置会员卡类型
                userRightsVo.setCardIconUrl(data.getCardIconUrl()); // 设置会员卡图标
            }
        }

        return userRightsVo;
    }

    /**
     * 查询用户系统权益分类列表
     * @param userId 用户id
     * @return
     */
    private List<UserSystemRightsCategVo> selectUserSystemRights(Long userId) {
        // 查询系统权益分类列表
        List<PlatfPoinsRightsCateg> list = platfPoinsRightsCategService.list();

        return list.stream().map(platfPoinsRightsCateg -> {
            UserSystemRightsCategVo userSystemRightsCategVo = new UserSystemRightsCategVo();
            // 设置分类名称
            userSystemRightsCategVo.setRuleCategName(platfPoinsRightsCateg.getRuleCategoryName());
            // 设置分类图标
            userSystemRightsCategVo.setRuleCategoryUrl(platfPoinsRightsCateg.getRuleCategoryUrl());
            // 查询改分类下的权益
            List<UserSystemRightsVo> userSystemRightsVoList = platformPoinsRulesMapper.selectUserSystemCategRights(userId, platfPoinsRightsCateg.getPoinsRuleCategId());
            // 设置权益列表
            userSystemRightsCategVo.setUserSystemRightsVoList(userSystemRightsVoList);
            // 设置权益数量
            userSystemRightsCategVo.setRightsCount(userSystemRightsVoList.size());

            return userSystemRightsCategVo;

        }).collect(Collectors.toList());

    }

}
