package zx.vanx.user_permiss.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import zx.vanx.user_permiss.entity.PlatfPointsCategory;
import zx.vanx.user_permiss.mapper.PlatfPointsCategoryMapper;
import zx.vanx.user_permiss.service.PlatfPointsCategoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * Vanx系统的积分的类别表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Service
public class PlatfPointsCategoryServiceImpl extends ServiceImpl<PlatfPointsCategoryMapper, PlatfPointsCategory> implements PlatfPointsCategoryService {

    /**
     * 查询积分类别
     * @param internalPointCategName 积分类别名称
     * @return 积分类别
     */
    @Override
    public PlatfPointsCategory getPlatfPointsCategoryByInternalPointCategName(String internalPointCategName) {

        QueryWrapper<PlatfPointsCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("internal_point_categ_name", internalPointCategName);

        return baseMapper.selectOne(queryWrapper);
    }
}
