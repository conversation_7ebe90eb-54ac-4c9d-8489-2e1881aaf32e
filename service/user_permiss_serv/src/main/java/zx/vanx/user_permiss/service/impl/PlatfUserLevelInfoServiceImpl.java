package zx.vanx.user_permiss.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import zx.vanx.user_permiss.entity.PlatfPoinsRightsRules;
import zx.vanx.user_permiss.entity.PlatfUserLevelInfo;
import zx.vanx.user_permiss.mapper.PlatfUserLevelInfoMapper;
import zx.vanx.user_permiss.service.PlatfPoinsRightsRulesService;
import zx.vanx.user_permiss.service.UserLevelInfoService;
import zx.vanx.user_permiss.vo.UserLevelSelectVo;
import zx.vanx.user_permiss.vo.VipCardLevelRightsVo;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * vanx_platf_user_level_info 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class PlatfUserLevelInfoServiceImpl extends ServiceImpl<PlatfUserLevelInfoMapper, PlatfUserLevelInfo> implements UserLevelInfoService {

    private final PlatfPoinsRightsRulesService platformPoinsRulesService;

    /**
     * 查询等级及其等级下的权益列表
     * @param userLevelType 等级类型
     * @return 等级及其等级下的权益列表
     */
    @Override
    public List<PlatfUserLevelInfo> queryLevelAndRightsList(String userLevelType) {

        QueryWrapper<PlatfUserLevelInfo> queryWrapper = new QueryWrapper<PlatfUserLevelInfo>();

        if (userLevelType.equals("全部")) {
            queryWrapper.orderByAsc("user_level_code");
        } else {
            queryWrapper.eq("user_level_type", userLevelType).orderByAsc("user_level_code");
        }

        List<PlatfUserLevelInfo> platfUserLevelInfoList = this.list(queryWrapper);

        if (!CollectionUtils.isEmpty(platfUserLevelInfoList)) {
            platfUserLevelInfoList = platfUserLevelInfoList.stream().map(userLevelInfo -> {

                List<PlatfPoinsRightsRules> poinsRulesList = platformPoinsRulesService.getBaseMapper().selectList(new QueryWrapper<PlatfPoinsRightsRules>().eq("level_info_id", userLevelInfo.getLevelInfoId()));
                userLevelInfo.setRightsList(poinsRulesList);

                return userLevelInfo;
            }).collect(Collectors.toList());
        }

        return platfUserLevelInfoList;
    }

    /**
     * 删除等级
     * @param levelInfoId 等级id
     */
    @Override
    public void removeLevelInfo(Long levelInfoId) {

        // TODO 删除该等级minio图片

        // 删除等级
        this.removeById(levelInfoId);

        // TODO 删除该权益minio图片

        // 删除等级权益
        platformPoinsRulesService.getBaseMapper().delete(new QueryWrapper<PlatfPoinsRightsRules>().eq("level_info_id",levelInfoId));

    }

    /**
     * 查询用户等级下拉列表
     * @param userLevelType 用户等级类型
     * @return 用户等级下拉列表
     */
    @Override
    public List<UserLevelSelectVo> queryUserLevelSelectList(String userLevelType) {

        return baseMapper.selectUserLevelSelectList(userLevelType);
    }

    /**
     * 会员卡查询等级权益基本信息
     * @param userBehaviorType 用户行为类型
     * @param levelInfoId 用户等级id
     * @return 等级权益基本信息
     */
    @Override
    public VipCardLevelRightsVo queryVipCardLevelRights(String userBehaviorType, Long levelInfoId) {

        return baseMapper.selectVipCardLevelRights(userBehaviorType,levelInfoId);
    }

    /**
     * 根据等级id查询等级信息
     * @param levelInfoId 等级id
     * @return 等级信息
     */
    @Override
    public PlatfUserLevelInfo queryUserLevelInfo(Long levelInfoId) {
        return baseMapper.selectById(levelInfoId);
    }
}
