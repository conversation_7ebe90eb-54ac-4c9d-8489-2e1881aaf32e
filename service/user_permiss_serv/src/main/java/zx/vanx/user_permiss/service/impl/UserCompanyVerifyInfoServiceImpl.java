package zx.vanx.user_permiss.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import zx.vanx.user_permiss.entity.UserCompanyVerifyInfo;
import zx.vanx.user_permiss.enums.ArtificialPersonStatusEnum;
import zx.vanx.user_permiss.mapper.UserCompanyVerifyInfoMapper;
import zx.vanx.user_permiss.service.UserCompanyVerifyInfoService;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 用户企业认证 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
@Service
public class UserCompanyVerifyInfoServiceImpl extends ServiceImpl<UserCompanyVerifyInfoMapper, UserCompanyVerifyInfo> implements UserCompanyVerifyInfoService {

    /**
     * 查询是否有企业认证记录
     * @param userId 用户id
     * @return 认证记录id
     */
    @Override
    public Long selectOneByUserId(Long userId) {

        QueryWrapper<UserCompanyVerifyInfo> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        wrapper.eq("artificial_person_status", ArtificialPersonStatusEnum.TONG_GUO.getValue());
        long tongGuoCount = this.count(wrapper);
        if (tongGuoCount > 0) {
            return tongGuoCount;
        }
        QueryWrapper<UserCompanyVerifyInfo> wrapper2 = new QueryWrapper<>();
        wrapper2.eq("user_id", userId);
        wrapper2.eq("artificial_person_status", ArtificialPersonStatusEnum.DAI_SHEN_HE.getValue());

        long daoShenHeCount = this.count(wrapper2);
        if (daoShenHeCount > 0) {
            return daoShenHeCount;
        }
        return 0L;
    }

    /**
     * 查询用户企业认证基本信息list
     * @param userId 用户id
     * @return 企业认证基本信息list
     */
    @Override
    public List<UserCompanyVerifyInfo> selectListByUserId(Long userId) {

        QueryWrapper<UserCompanyVerifyInfo> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        wrapper.orderByDesc("user_com_verify_id");
        return this.baseMapper.selectList(wrapper);
    }

    /**
     * 查询用户企业认证基本信息列表
     * @param userId 用户id
     * @return 企业认证基本信息列表
     */
    @Override
    public List<UserCompanyVerifyInfo> selectUserCompanyAuthInfoList(Long userId) {
        QueryWrapper<UserCompanyVerifyInfo> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        wrapper.eq("artificial_person_status", ArtificialPersonStatusEnum.TONG_GUO.getValue());
        wrapper.orderByDesc("user_com_verify_id");
        List<UserCompanyVerifyInfo> userCompanyVerifyInfoList = this.baseMapper.selectList(wrapper);
        if (userCompanyVerifyInfoList != null && !userCompanyVerifyInfoList.isEmpty()) {
            return userCompanyVerifyInfoList;
        } else {
            return Collections.emptyList();
        }
    }
}
