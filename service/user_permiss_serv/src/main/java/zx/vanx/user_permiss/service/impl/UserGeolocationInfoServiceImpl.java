package zx.vanx.user_permiss.service.impl;

import lombok.RequiredArgsConstructor;
import zx.vanx.common_util.core.redis.RedisCache;
import zx.vanx.common_util.utils.ObjectUtil;
import zx.vanx.user_permiss.entity.UserGeolocationInfo;
import zx.vanx.user_permiss.mapper.UserGeolocationInfoMapper;
import zx.vanx.user_permiss.redis.key.PermissKey;
import zx.vanx.user_permiss.service.UserGeolocationInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户地理位置信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-13
 */
@Service
@RequiredArgsConstructor
public class UserGeolocationInfoServiceImpl extends ServiceImpl<UserGeolocationInfoMapper, UserGeolocationInfo> implements UserGeolocationInfoService {

    private final RedisCache redisCache;

    /**
     * 保存或修改用户地理位置信息
     * @param userGeolocationInfo 用户地理位置信息
     */
    @Override
    public boolean saveOrUpdateGeolocationInfo(UserGeolocationInfo userGeolocationInfo) {
        if (!ObjectUtil.isEmpty(userGeolocationInfo.getUserGeolocatId())) {
            boolean b = updateById(userGeolocationInfo);
            redisCache.setCacheObject(PermissKey.USER_GEOLOCATION + userGeolocationInfo.getUserId(),userGeolocationInfo);

            // 更新操作
            return b;
        } else {
            redisCache.setCacheObject(PermissKey.USER_GEOLOCATION + userGeolocationInfo.getUserId(),userGeolocationInfo);
            // 保存操作
            return save(userGeolocationInfo);
        }
    }
}
