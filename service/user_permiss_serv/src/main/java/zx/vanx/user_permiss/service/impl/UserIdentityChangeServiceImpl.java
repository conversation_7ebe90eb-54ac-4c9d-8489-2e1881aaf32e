package zx.vanx.user_permiss.service.impl;

import zx.vanx.user_permiss.entity.UserIdentityChange;
import zx.vanx.user_permiss.mapper.UserIdentityChangeMapper;
import zx.vanx.user_permiss.service.UserIdentityChangeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户身份改变系统设置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
@Service
public class UserIdentityChangeServiceImpl extends ServiceImpl<UserIdentityChangeMapper, UserIdentityChange> implements UserIdentityChangeService {

}
