package zx.vanx.user_permiss.service.impl;

import zx.vanx.user_permiss.entity.UserIdentityLog;
import zx.vanx.user_permiss.mapper.UserIdentityLogMapper;
import zx.vanx.user_permiss.service.UserIdentityLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户身份记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-01
 */
@Service
public class UserIdentityLogServiceImpl extends ServiceImpl<UserIdentityLogMapper, UserIdentityLog> implements UserIdentityLogService {

}
