package zx.vanx.user_permiss.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import zx.vanx.user_permiss.entity.UserLabelRelat;
import zx.vanx.user_permiss.mapper.UserLabelRelatMapper;
import zx.vanx.user_permiss.service.UserLabelRelatService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * vanx_user_label_relat 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class UserLabelRelatServiceImpl extends ServiceImpl<UserLabelRelatMapper, UserLabelRelat> implements UserLabelRelatService {

}
