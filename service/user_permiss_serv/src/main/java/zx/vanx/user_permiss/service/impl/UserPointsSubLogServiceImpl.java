package zx.vanx.user_permiss.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import zx.vanx.common_util.utils.BeanUtil;
import zx.vanx.user_permiss.entity.PlatfPointsCategory;
import zx.vanx.user_permiss.entity.UserPointsSubLog;
import zx.vanx.user_permiss.mapper.UserPointsSubLogMapper;
import zx.vanx.user_permiss.request.NoSystemPointsReduceRequest;
import zx.vanx.user_permiss.service.PlatfPointsCategoryService;
import zx.vanx.user_permiss.service.UserPointsSubLogService;
import zx.vanx.user_permiss.vo.DynamicPointsVo;

import java.util.Date;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-19
 */
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class UserPointsSubLogServiceImpl extends ServiceImpl<UserPointsSubLogMapper, UserPointsSubLog> implements UserPointsSubLogService {

    private final PlatfPointsCategoryService platfPointsCategoryService;
    private final UserPointsSubLogMapper userPointsSubLogMapper;

    /**
     * 查询用户支出的动态积分记录list
     * @param userId 用户id
     * @param recentDateList 近期日期列表
     * @return 动态积分记录list
     */
    @Override
    public List<DynamicPointsVo> selectUserRecentPointsList(Long userId, List<String> recentDateList) {

        PlatfPointsCategory behaviorPoints = platfPointsCategoryService.getPlatfPointsCategoryByInternalPointCategName("行为积分");
        PlatfPointsCategory shoppingPoints = platfPointsCategoryService.getPlatfPointsCategoryByInternalPointCategName("购物积分");
        PlatfPointsCategory gamePoints = platfPointsCategoryService.getPlatfPointsCategoryByInternalPointCategName("游戏积分");

        QueryWrapper<UserPointsSubLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.in("created_date", recentDateList);
        queryWrapper.orderByDesc("created_date");
        List<UserPointsSubLog> userPointsSubLogList = this.list(queryWrapper);

        List<DynamicPointsVo> dynamicPointsVoList = BeanUtil.copyList(userPointsSubLogList, DynamicPointsVo.class);

        if (!CollectionUtils.isEmpty(dynamicPointsVoList)) {

            for (DynamicPointsVo dynamicPointsVo : dynamicPointsVoList) {
                if (dynamicPointsVo.getUserPointsType().equals("行为积分")) {
                    dynamicPointsVo.setUserPointsUrl(behaviorPoints.getCategPicUrl());
                }

                if (dynamicPointsVo.getUserPointsType().equals("购物积分")) {
                    dynamicPointsVo.setUserPointsUrl(shoppingPoints.getCategPicUrl());
                }

                if (dynamicPointsVo.getUserPointsType().equals("游戏积分")) {
                    dynamicPointsVo.setUserPointsUrl(gamePoints.getCategPicUrl());
                }

            }
        }

        return dynamicPointsVoList;
    }

    /**
     * 非系统配置行为积分-减少
     * @param noSystemPointsReduceRequest 非系统配置行为积分-减少请求
     * @param userId 用户id
     */
    @Override
    public void noSystemPointsReduce(NoSystemPointsReduceRequest noSystemPointsReduceRequest, Long userId) {
        // 记录该次加分日志
        UserPointsSubLog userPointsSubLog = BeanUtil.copyProperties(noSystemPointsReduceRequest, UserPointsSubLog::new); // 复制属性
        userPointsSubLog.setValueChangeType("减少"); // 设置改变类型,增加
        userPointsSubLog.setCreatedDate(new Date());
        userPointsSubLog.setCreatorId(noSystemPointsReduceRequest.getUserId()); // 设置创建者id
        userPointsSubLogMapper.insert(userPointsSubLog); // 插入数据库
    }
}
