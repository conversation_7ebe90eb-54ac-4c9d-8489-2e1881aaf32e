package zx.vanx.user_permiss.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import zx.vanx.common_util.exception.ZxException;
import zx.vanx.user_permiss.entity.UserPrivSetting;
import zx.vanx.user_permiss.enums.RuleName;
import zx.vanx.user_permiss.mapper.UserPrivSettingMapper;
import zx.vanx.user_permiss.service.UserPrivSettingService;
import zx.vanx.user_permiss.vo.AppPassword;
import zx.vanx.user_permiss.vo.loginAppInfo;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class UserPrivSettingServiceImpl extends ServiceImpl<UserPrivSettingMapper, UserPrivSetting> implements UserPrivSettingService {

    private final UserPrivSettingMapper userPrivSettingMapper;

    /**
     * 用户是否开启隐藏功能
     * @param currentUserId 当前用户ID
     * @return true:开启隐藏功能，false:未开启隐藏功能
     */
    @Override
    public Boolean userIsOpenHideFunction(Long currentUserId) {

        // 查询用户是否开启隐藏功能
        UserPrivSetting userPrivSetting = userPrivSettingMapper.selectOneByUserIdAndRuleName(currentUserId, RuleName.HIDE_SETTINGS.getName());

        return !ObjectUtils.isEmpty(userPrivSetting);
    }

    /**
     * 当前用户是否设置启动时加密，若设置，密码类型是啥
     * @param currentUserId 当前用户ID
     * @param encryptMethod 加密方式: 启动时加密、进入应用加密、查看作品加密
     * @return AppPassword
     */
    @Override
    public AppPassword userIsSettingEncryptMethod(Long currentUserId, String encryptMethod) {

        // 查询用户是否开启加密功能
        UserPrivSetting encryptSetting = userPrivSettingMapper.selectOneByUserIdAndRuleName(currentUserId, RuleName.ENCRYPTION_SETTINGS.getName());
        AppPassword appPassword = new AppPassword();
        if (!ObjectUtils.isEmpty(encryptSetting)) {
            if (encryptSetting.getRuleValueName().equals(encryptMethod)) {
                // 获取密码类型设置
                UserPrivSetting passwordTypeSetting = userPrivSettingMapper.selectOneByUserIdAndRuleName(currentUserId, RuleName.PASSWORD_TYPE.getName());

                appPassword.setIsSettingPassword(true);
                appPassword.setPasswordType(passwordTypeSetting.getRuleValueName());
                return appPassword;
            }
        }

        appPassword.setIsSettingPassword(false);
        return appPassword;
    }

    /**
     * App用户登录
     * @param loginAppInfo 用户登录信息
     * @return true:登录成功，false:登录失败
     */
    @Override
    public Boolean loginApp(loginAppInfo loginAppInfo) {

        UserPrivSetting userPrivSetting = userPrivSettingMapper.selectOneByUserIdAndRuleNameAndRuleValueName(loginAppInfo.getUserId(), RuleName.PASSWORD_TYPE.getName(), loginAppInfo.getPasswordType());

        if (!ObjectUtils.isEmpty(userPrivSetting)) {
            if (userPrivSetting.getRuleValue().equals(loginAppInfo.getPassword())) {
                return true;
            }
        } else {
            throw new ZxException(201,"密码错误");
        }
        return false;
    }

}
