package zx.vanx.user_permiss.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import zx.vanx.user_permiss.entity.UserVerifyInfo;
import zx.vanx.user_permiss.enums.ArtificialPersonStatusEnum;
import zx.vanx.user_permiss.mapper.UserVerifyInfoMapper;
import zx.vanx.user_permiss.service.UserVerifyInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户个人认证 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-04
 */
@Service
public class UserVerifyInfoServiceImpl extends ServiceImpl<UserVerifyInfoMapper, UserVerifyInfo> implements UserVerifyInfoService {

    /**
     * 查询是否有个人认证记录
     * @param userId 用户id
     * @return 记录数
     */
    @Override
    public Long selectCountByUserId(Long userId) {

        QueryWrapper<UserVerifyInfo> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        wrapper.eq("artificial_person_status", ArtificialPersonStatusEnum.TONG_GUO.getValue());
        long tongGuoCount = this.count(wrapper);
        if (tongGuoCount > 0) {
            return tongGuoCount;
        }
        QueryWrapper<UserVerifyInfo> wrapper2 = new QueryWrapper<>();
        wrapper2.eq("user_id", userId);
        wrapper2.eq("artificial_person_status", ArtificialPersonStatusEnum.DAI_SHEN_HE.getValue());

        long daoShenHeCount = this.count(wrapper2);
        if (daoShenHeCount > 0) {
            return daoShenHeCount;
        }
        return 0L;
    }

    /**
     * 查询用户个人认证基本信息列表
     * @param userId 用户id
     * @return 个人认证基本信息列表
     */
    @Override
    public List<UserVerifyInfo> selectListByUserId(Long userId) {

        QueryWrapper<UserVerifyInfo> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        wrapper.orderByDesc("user_verify_id");

        return this.baseMapper.selectList(wrapper);
    }
}
