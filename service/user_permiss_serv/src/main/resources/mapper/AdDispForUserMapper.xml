<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.user_permiss.mapper.AdDispForUserMapper">

    <!--根据用户id和广告id查询广告弹出记录-->
    <select id="selectOneByUserIdAndDisplayAdId" resultType="zx.vanx.user_permiss.entity.AdDispForUser">
        select * from vanx_ad_disp_for_user where user_id = #{userId} and display_ad_id = #{displayAdId} and is_deleted = 0
    </select>
    <!--根据用户id和广告id删除广告弹出记录-->
    <delete id="deleteByUserIdAndDisplayAdId">
        delete from vanx_ad_disp_for_user where user_id = #{userId} and display_ad_id = #{displayAdId}
    </delete>

    <!--根据用户id查询广告列表-->
    <select id="selectListByUserId" resultType="zx.vanx.user_permiss.entity.AdDispForUser">
        select * from vanx_ad_disp_for_user where user_id = #{userId} and is_deleted = 0
    </select>
</mapper>
