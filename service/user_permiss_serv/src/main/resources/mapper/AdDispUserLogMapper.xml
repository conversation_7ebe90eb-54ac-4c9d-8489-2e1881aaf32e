<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.user_permiss.mapper.AdDispUserLogMapper">

    <!--根据用户id和广告id查询广告弹出记录-->
    <select id="selectOneByUserIdAndDisplayAdId" resultType="zx.vanx.user_permiss.entity.AdDispUserLog">
        SELECT *
        FROM vanx_ad_disp_user_log
        WHERE user_id = #{userId} AND display_ad_id = #{displayAdId} AND ad_display_time &lt;= NOW()
        ORDER BY ad_display_time DESC
        LIMIT 1;
    </select>
</mapper>
