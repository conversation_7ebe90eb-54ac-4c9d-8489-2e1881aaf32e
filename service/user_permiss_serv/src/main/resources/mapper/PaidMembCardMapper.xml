<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.user_permiss.mapper.PaidMembCardMapper">

    <!--查询用户当前购买的有效会员卡-->
    <select id="selectUserEffectiveMembCard" resultType="zx.vanx.user_permiss.entity.PaidMembCard">
        SELECT *
        FROM vanx_paid_memb_card
        WHERE NOW() BETWEEN use_rule_start_time AND user_rule_end_time
          and is_deleted = 0 and is_effective = 1
    </select>
</mapper>
