<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.user_permiss.mapper.PlatfRolesMapper">


    <!--根据角色名称查询角色信息-->
    <select id="selectOneByRoleName" resultType="zx.vanx.user_permiss.entity.PlatfRoles">
        select * from vanx_platf_roles where role_name = #{roleName} and is_deleted = 0 and role_status = 1
    </select>
</mapper>
