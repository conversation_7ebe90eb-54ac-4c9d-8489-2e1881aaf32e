<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.user_permiss.mapper.PlatfUserLabelMapper">

    <select id="selectAllByUserId" resultType="zx.vanx.user_permiss.entity.PlatfUserLabel">
        select *
        from vanx_platf_user_label vpul
        left join vanx_user_label_relat vulr on vpul.user_label_id = vulr.label_id
        where vulr.user_id = #{userId} and vulr.is_deleted = 0
    </select>
</mapper>
