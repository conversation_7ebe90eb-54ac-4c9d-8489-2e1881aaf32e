<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.user_permiss.mapper.PlatfUserLevelInfoMapper">

    <!--查询用户等级id-->
    <select id="selectLevelId" resultType="long">
        select level_info_id
        from vanx_platf_user_level_info
        where user_level_type = #{userLevelType}
          and #{PoinsValue} &gt;= required_min_poins
          and #{PoinsValue} &lt; required_max_poins
          and is_deleted = 0
    </select>

    <!--查询用户等级下拉列表-->
    <select id="selectUserLevelSelectList" resultType="zx.vanx.user_permiss.vo.UserLevelSelectVo">
        SELECT level_info_id,user_level_name
        FROM `vanx_platf_user_level_info`
        where user_level_type = #{userLevelType}
        and is_deleted = 0
    </select>

    <!--会员卡查询等级权益基本信息-->
    <select id="selectVipCardLevelRights" resultType="zx.vanx.user_permiss.vo.VipCardLevelRightsVo">
        select
            poins_rule_id,
            rule_name,
            user_behavior_type,
            rule_category_name,
            rule_object_value,
            rule_obj_value_time_unit,
            rule_object_type,
            rule_object_get_points
        from vanx_platf_poins_rights_rules
        where user_behavior_type = #{userBehaviorType}
        and level_info_id = #{levelInfoId}
        and is_deleted = 0
    </select>

    <!--查询等级相关信息-->
    <select id="selectByUserLevelTypeAndUserLevelName" resultType="zx.vanx.user_permiss.entity.PlatfUserLevelInfo">
        select * from vanx_platf_user_level_info
        where user_level_type = #{userLevelType}
          and user_level_name = #{userLevelName}
          and is_deleted = 0
    </select>

    <select id="selectUserLevelInfo" resultType="zx.vanx.user_permiss.entity.PlatfUserLevelInfo">
        SELECT
            vpli.*
        FROM
            user_info_fast uif
                INNER JOIN
            vanx_platf_user_level_info vpli ON uif.growth_value BETWEEN vpli.required_min_poins AND vpli.required_max_poins
        WHERE
            uif.user_id = #{userId}
          AND vpli.user_level_type = '成长值等级' and vpli.is_deleted = 0
    </select>

    <!--查询用户下一级等级-->
    <select id="selectLevelInfoBySort" resultType="zx.vanx.user_permiss.entity.PlatfUserLevelInfo">
        SELECT * FROM vanx_platf_user_level_info WHERE user_level_type = '成长值等级' and sort_number = #{sortNumber} and is_deleted = 0
    </select>
</mapper>
