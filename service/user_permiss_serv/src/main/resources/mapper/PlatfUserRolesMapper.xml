<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.user_permiss.mapper.PlatfUserRolesMapper">

    <!--将所有角色的isActive设为false-->
    <update id="updateIsActiveToFalse">
        UPDATE vanx_platf_user_roles SET is_active = false;
    </update>

    <!--根据userId和roleName查询用户角色关联表-->
    <select id="selectOneByUserIdAndRoleName" resultType="zx.vanx.user_permiss.entity.PlatfUserRoles">

        select vpur.* from vanx_platf_user_roles vpur
                          left join vanx_platf_roles vpr on vpur.role_id = vpr.role_id
        where vpur.user_id = #{userId} and vpr.role_name = #{roleName} and vpur.is_deleted = 0

    </select>
    <!--根据roleName查询用户角色关联表 -->
    <select id="existsByRoleName" resultType="java.lang.Boolean">
        SELECT EXISTS (
            SELECT 1
            FROM vanx_platf_user_roles vpur
                     LEFT JOIN vanx_platf_roles vpr
                               ON vpur.role_id = vpr.role_id
            WHERE vpr.role_name = #{roleName}
              AND vpur.user_id = #{userId}
              AND vpur.is_deleted = 0
        ) AS has_data;
    </select>

</mapper>
