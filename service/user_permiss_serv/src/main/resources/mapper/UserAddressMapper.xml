<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.user_permiss.mapper.UserAddressMapper">

    <!--查询用户收货地址列表-->
    <select id="selectUserRecieveAddressList" resultType="zx.vanx.user_permiss.entity.UserAddress">

        select * from user_address where location_type = '收件地址'
                                     and user_id = #{userId}
                                     and is_deleted = 0
                                     and info_address_status = 1

    </select>
</mapper>
