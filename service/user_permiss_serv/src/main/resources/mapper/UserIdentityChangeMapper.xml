<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.user_permiss.mapper.UserIdentityChangeMapper">

    <!--用户身份改变系统设置,根据用户身份改变或切换名称查询-->
    <select id="selectOneByUserIdenChangeName" resultType="zx.vanx.user_permiss.entity.UserIdentityChange">
        select * from vanx_user_identity_change where user_iden_change_name = #{UserIdenChangeName} and is_deleted = 0
    </select>
</mapper>
