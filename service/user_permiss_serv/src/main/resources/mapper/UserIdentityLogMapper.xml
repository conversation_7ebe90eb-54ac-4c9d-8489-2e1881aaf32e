<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.user_permiss.mapper.UserIdentityLogMapper">

    <!--查询用户身份类型列表-->
    <select id="selectUserTypeList" resultType="zx.vanx.user_permiss.entity.UserIdentityLog">
        (
            SELECT *
            FROM `vanx_user_identity_log`
            WHERE is_valid = true
              AND is_deleted = 0
              AND user_id = #{userId}
              AND user_categ_name = '升级类用户'
            ORDER BY created_time DESC
            LIMIT 1
        )
        UNION
        (
            SELECT *
            FROM `vanx_user_identity_log`
            WHERE is_valid = true
              AND is_deleted = 0
              AND user_id = #{userId}
              AND user_categ_name = '付费类用户'
            ORDER BY created_time DESC
            LIMIT 1
        )
        ORDER BY created_time DESC;
    </select>
</mapper>
