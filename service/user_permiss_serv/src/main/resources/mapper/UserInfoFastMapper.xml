<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.user_permiss.mapper.UserInfoFastMapper">

    <!--根据用户id查询用户fast信息-->
    <select id="selectOneByUserId" resultType="zx.vanx.user_permiss.entity.UserInfoFast">
        select *
        from user_info_fast
        where
        user_id = #{userId}
        and is_deleted = 0
    </select>

    <!--修改用户广告弹出状态为true-->
    <update id="updateAdPopStatusByUserId">
        update user_info_fast set need_pop_up_advert = true where is_deleted = 0 and user_id = #{userId}
    </update>


</mapper>
