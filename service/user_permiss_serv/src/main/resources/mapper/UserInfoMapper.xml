<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.user_permiss.mapper.UserInfoMapper">

    <!--查询用户角色列表-->
    <select id="selectUserRoleList" resultType="zx.vanx.user_permiss.vo.UserRoleVo">
        select vpr.role_name,vpr.role_id,vpur.is_active,vpr.role_categ_name from vanx_platf_user_roles vpur
                                            LEFT JOIN vanx_platf_roles vpr on vpur.role_id = vpr.role_id
        where vpur.user_id = #{userId} and vpur.is_deleted = 0
    </select>


    <!--查询用户当前活跃角色-->
    <select id="selectUserCurrentlyActiveRole" resultType="java.lang.String">
        select role_name from vanx_platf_user_roles vpur
                                            LEFT JOIN vanx_platf_roles vpr on vpur.role_id = vpr.role_id
        where vpur.user_id = #{userId} and vpur.is_active = 1 and vpur.is_deleted = 0
    </select>

    <!--查询用户总数-->
    <select id="selectUserTotalCount" resultType="java.lang.Long">
        select count(*) from user_info
        where is_deleted = 0
        <if test="userNickname != null and userNickname != ''">
            and user_nickname = #{userNickname}
        </if>
        <if test="tel != null and tel != ''">
            and tel = #{tel}
        </if>
    </select>

    <select id="selectManagerUserRecords" resultType="zx.vanx.user_permiss.vo.ManagerUserInfoVo">
        select
        ui.user_id,
        ui.user_nickname,
        ui.user_internal_code as userVanxId,
        COALESCE(
        (select picture_url
        from user_picture
        where picture_type = '个人头像'
        and user_id = ui.user_id
        and is_deleted = 0
        limit 1),
        (select picture_url
        from user_picture
        where picture_type = '个人头像'
        and add_source = '平台添加'
        and is_deleted = 0
        limit 1)
        ) as userAvatar,
        COALESCE(
        (select picture_url
        from user_picture
        where picture_type = '个人封面照片'
        and user_id = ui.user_id
        and is_deleted = 0
        limit 1),
        (select picture_url
        from user_picture
        where picture_type = '个人封面照片'
        and add_source = '平台添加'
        and is_deleted = 0
        limit 1)
        ) as userCover,
        uei.user_personal_signa as userSignature
        from
        user_info ui
        left join
        user_extend_info uei on uei.user_id = ui.user_id
        where
        ui.is_deleted = 0
        <if test="userNickname != null and userNickname != ''">
            and ui.user_nickname = #{userNickname}
        </if>
        <if test="tel != null and tel != ''">
            and ui.tel = #{tel}
        </if>
        limit #{offset}, #{limit}
    </select>

    <!--根据用户昵称查询用户信息-->
    <select id="selectByUserNickname" resultType="zx.vanx.user_permiss.entity.UserInfo">
        select * from user_info where user_nickname = #{userNickname} and is_deleted = 0
    </select>

    <!--查询真实用户附近随机30人-->
    <select id="selectNearbyRealUser" resultType="zx.vanx.user_permiss.entity.UserInfo">
        select * from user_info where is_deleted = 0 and add_source = '自定义添加' and user_id not in (#{userId}) order by RAND() limit 2
    </select>

    <!--查询系统用户随机30人-->
    <select id="selectNearbySysUser" resultType="zx.vanx.user_permiss.entity.UserInfo">
        select * from user_info where is_deleted = 0 and add_source = '平台添加' and user_id not in (#{userId}) order by RAND() limit 5
    </select>

    <!--查询用户身份切换列表-->
    <select id="userIdentitySwitchList" resultType="zx.vanx.user_permiss.vo.UserIdentitySwitch">
        select vpur.user_role_id,vpur.is_active,vpr.role_name,vpr.frontend_show_name from vanx_platf_user_roles vpur left join vanx_platf_roles vpr on vpur.role_id = vpr.role_id
        where vpur.user_id = #{userId} and vpr.role_categ_name=#{roleCategName} and vpur.is_deleted=0
    </select>
</mapper>
