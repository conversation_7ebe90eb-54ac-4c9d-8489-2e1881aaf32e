<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.user_permiss.mapper.UserRegisterMapper">
    <!--根据用户id查询用户注册信息-->
    <select id="selectByUserId" resultType="zx.vanx.user_permiss.entity.UserRegister">
        select * from user_register where user_id = #{userId} and is_deleted = 0
    </select>
</mapper>
