<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="zx.vanx.user_permiss.mapper.UserVerifyInfoMapper">

    <!--根据用户ID和认证来源查询最新认证信息-->
    <select id="selectLatestByUserAndSourceAndStatus" resultType="zx.vanx.user_permiss.entity.UserVerifyInfo">

        SELECT
            user_verify_id,
            user_id,
            linkman,
            linkman_phone,
            artificial_person_name,
            artificial_person_no,
            id_pic_front_url,
            id_pic_front_minio_name,
            id_pic_back_url,
            id_pic_back_minio_name,
            add_source,
            id_type,
            artificial_person_status,
            reject_reason,
            platform_id,
            platform_user_id,
            pass_time,
            creator_id,
            editor_id,
            created_time,
            modified_time,
            is_deleted,
            other_info_one,
            other_info_two,
            remark
        FROM (
                 SELECT
                     *,
                     ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_time DESC) AS rn
                 FROM
                     user_verify_info
                 WHERE
                     is_deleted = 0 and artificial_person_status = #{artificialPersonStatus}
                 ORDER BY user_verify_id DESC
             ) AS subquery
        WHERE
            rn = 1

    </select>

    <!--根据认证来源查询最新认证信息-->
    <select id="selectLatestByUserAndSource" resultType="zx.vanx.user_permiss.entity.UserVerifyInfo">
        SELECT
            user_verify_id,
            user_id,
            linkman,
            linkman_phone,
            artificial_person_name,
            artificial_person_no,
            id_pic_front_url,
            id_pic_front_minio_name,
            id_pic_back_url,
            id_pic_back_minio_name,
            add_source,
            id_type,
            artificial_person_status,
            reject_reason,
            platform_id,
            platform_user_id,
            pass_time,
            creator_id,
            editor_id,
            created_time,
            modified_time,
            is_deleted,
            other_info_one,
            other_info_two,
            remark
        FROM (
                 SELECT
                     *,
                     ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_time DESC) AS rn
                 FROM
                     user_verify_info
                 WHERE
                     is_deleted = 0
                 ORDER BY user_verify_id DESC
             ) AS subquery
        WHERE
            rn = 1

    </select>
</mapper>
